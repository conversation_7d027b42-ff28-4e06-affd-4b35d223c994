<template>
  <div class="ai-config-container">
    <div class="page-header">
      <h2>AI配置管理</h2>
      <p>配置AI模型参数和连接设置</p>
    </div>

    <div class="main-content">
      <div class="prompts-sidebar">
        <div class="sidebar-header">
          <h3>提示词目录</h3>
          <el-button type="primary" size="small" @click="handleAddPrompt" class="add-btn">
            <el-icon><Plus /></el-icon>
            新增
          </el-button>
        </div>

        <div class="search-box">
          <el-input
            ref="searchInputRef"
            v-model="searchKeyword"
            placeholder="搜索提示词"
            @input="handleSearch"
            @keydown.enter="handleSearch"
            clearable
            :loading="loading"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
            <template #suffix v-if="searchKeyword && !loading">
              <span class="search-info">{{ filteredPrompts.length }} 个结果</span>
            </template>
          </el-input>
        </div>

        <div class="prompts-list" v-loading="loading">
          <!-- 空状态提示 -->
          <div v-if="!loading && filteredPrompts.length === 0" class="empty-prompts">
            <el-empty
              :description="searchKeyword ? `未找到包含 ${searchKeyword} 的提示词` : '暂无提示词数据'"
              :image-size="80"
            >
              <el-button v-if="!searchKeyword" type="primary" @click="handleAddPrompt">
                <el-icon><Plus /></el-icon>
                创建第一个提示词
              </el-button>
            </el-empty>
          </div>

          <!-- 提示词列表 -->
          <div
            v-for="prompt in filteredPrompts"
            :key="prompt?.prompt_id || Math.random()"
            :class="['prompt-item', { active: safeSelectedPrompt?.prompt_id === prompt?.prompt_id }]"
            @click="handleSelectPrompt(prompt)"
          >
            <div class="prompt-info">
              <h4 class="prompt-title">{{ prompt?.title || '未命名提示词' }}</h4>
              <p class="prompt-desc">{{ prompt?.description || '暂无描述' }}</p>
              <div class="prompt-meta">
                <el-tag size="small" type="info">{{ prompt?.category || '未分类' }}</el-tag>
                <span class="usage-count">使用 {{ prompt?.usage_count || 0 }} 次</span>
              </div>
            </div>
            <div class="prompt-actions">
              <!-- 选择/正在使用按钮 - 固定位置 -->
              <div class="action-slot">
                <el-button
                  size="small"
                  @click.stop="activePromptId !== prompt?.prompt_id ? handleSetActivePrompt(prompt) : null"
                  :loading="settingActivePrompt"
                  :class="activePromptId === prompt?.prompt_id ? 'active-btn' : 'select-btn'"
                  :title="activePromptId === prompt?.prompt_id ? '正在使用' : '选择使用'"
                  :disabled="activePromptId === prompt?.prompt_id"
                  circle
                >
                  <el-icon><Check /></el-icon>
                </el-button>
              </div>

              <!-- 删除按钮 - 固定位置 -->
              <div class="action-slot">
                <el-button
                  size="small"
                  type="danger"
                  @click.stop="handleDeletePrompt(prompt)"
                  class="delete-btn"
                  circle
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="detail-panel">
        <div v-if="currentMode === 'view'" class="detail-view">
          <div class="detail-header">
            <h3>{{ safeSelectedPrompt?.title || '提示词详情' }}</h3>
            <div class="detail-actions">
              <el-button type="primary" @click="handleEditPrompt">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
            </div>
          </div>

          <div class="detail-content">
            <div class="detail-section">
              <h4>描述</h4>
              <p>{{ safeSelectedPrompt?.description || '暂无描述' }}</p>
            </div>

            <div class="detail-section">
              <h4>提示词内容</h4>
              <div class="content-display prompt-content-display">
                <pre>{{ safeSelectedPrompt?.content || '暂无内容' }}</pre>
              </div>
            </div>

            <div class="detail-section">
              <h4>变量</h4>
              <div class="variables-list">
                <el-tag
                  v-for="variable in safeSelectedPrompt?.variables || []"
                  :key="variable"
                  type="warning"
                  class="variable-tag"
                >
                  {{ variable }}
                </el-tag>
                <span v-if="!safeSelectedPrompt?.variables?.length" class="no-variables">无变量</span>
              </div>
            </div>
          </div>
        </div>

        <div v-if="currentMode === 'edit' || currentMode === 'add'" class="form-panel">
          <div class="form-header">
            <h3>{{ currentMode === 'add' ? '新增提示词' : '编辑提示词' }}</h3>
            <div class="form-actions">
              <el-button @click="handleCancel">取消</el-button>
              <el-button type="primary" @click="handleSave" :loading="saving">
                <el-icon><Check /></el-icon>
                保存
              </el-button>
            </div>
          </div>

          <div class="form-content">
            <el-form
              ref="promptFormRef"
              :model="promptForm"
              :rules="formRules"
              label-width="100px"
              label-position="top"
            >
              <el-form-item label="标题" prop="title">
                <el-input
                  v-model="promptForm.title"
                  placeholder="请输入提示词标题"
                  maxlength="100"
                  show-word-limit
                />
              </el-form-item>

              <el-form-item label="描述" prop="description">
                <el-input
                  v-model="promptForm.description"
                  type="textarea"
                  placeholder="请输入提示词描述"
                  :rows="3"
                  maxlength="500"
                  show-word-limit
                />
              </el-form-item>

              <el-form-item label="提示词内容" prop="content">
                <el-input
                  v-model="promptForm.content"
                  type="textarea"
                  placeholder="请输入提示词内容，使用花括号格式定义变量，如{变量名}"
                  :rows="12"
                  maxlength="5000"
                  show-word-limit
                  class="prompt-content-textarea"
                />
                <div class="content-help">
                  <el-icon><InfoFilled /></el-icon>
                  <span>提示：使用花括号格式定义变量，如 requirements、code_content 等</span>
                </div>
              </el-form-item>

              <el-form-item label="分类" prop="category">
                <el-select v-model="promptForm.category" placeholder="选择分类">
                  <el-option label="通用" value="general" />
                  <el-option label="测试用例" value="test_case" />
                  <el-option label="缺陷分析" value="bug_analysis" />
                  <el-option label="代码审查" value="code_review" />
                </el-select>
              </el-form-item>

              <el-form-item label="标签" prop="tags">
                <el-select
                  v-model="promptForm.tags"
                  multiple
                  filterable
                  allow-create
                  placeholder="添加标签"
                  class="tags-select"
                >
                  <el-option label="测试用例" value="测试用例" />
                  <el-option label="缺陷分析" value="缺陷分析" />
                  <el-option label="代码审查" value="代码审查" />
                  <el-option label="自动生成" value="自动生成" />
                  <el-option label="问题解决" value="问题解决" />
                </el-select>
              </el-form-item>

              <el-form-item label="公开设置">
                <el-switch v-model="promptForm.is_public" active-text="公开" inactive-text="私有" />
              </el-form-item>
            </el-form>
          </div>
        </div>

        <div v-if="currentMode === 'default'" class="default-state">
          <el-empty description="请选择一个提示词查看详情，或点击新增按钮创建新的提示词" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, computed, watch } from 'vue'
import { ElMessage, ElMessageBox, ElForm } from 'element-plus'
import { Plus, Edit, Delete, Search, Check, InfoFilled } from '@element-plus/icons-vue'
import { useDebounceFn } from '@vueuse/core'
import { apiService, type PromptItem } from '@/services/api'

// 使用API服务中定义的PromptItem类型，不再重复定义
type Prompt = PromptItem

interface PromptForm {
  title: string
  content: string
  description: string
  category: string
  status: string
  tags: string[]
  is_public: boolean
}

const loading = ref(false)
const saving = ref(false)
const searchKeyword = ref('')
const currentMode = ref<'default' | 'view' | 'edit' | 'add'>('default')
const selectedPrompt = ref<Prompt | null>(null)
const promptsList = ref<Prompt[]>([])
const promptFormRef = ref<InstanceType<typeof ElForm>>()
const searchInputRef = ref<any>(null)

// 活跃提示词状态
const activePromptId = ref<string | null>(null)
const settingActivePrompt = ref(false)

const safeSelectedPrompt = computed(() => {
  const prompt = selectedPrompt.value

  if (!prompt || typeof prompt !== 'object') {
    return null
  }

  const result = {
    prompt_id: prompt.prompt_id || '',
    title: prompt.title || '',
    content: prompt.content || '',
    description: prompt.description || '',
    category: prompt.category || 'general',
    status: prompt.status || 'active',
    tags: Array.isArray(prompt.tags) ? prompt.tags : [],
    variables: Array.isArray(prompt.variables) ? prompt.variables : [],
    is_public: typeof prompt.is_public === 'boolean' ? prompt.is_public : true,
    usage_count: typeof prompt.usage_count === 'number' ? prompt.usage_count : 0,
    created_at: prompt.created_at || '',
    updated_at: prompt.updated_at || ''
  }

  console.log('✅ safeSelectedPrompt 计算结果:', result)
  return result
})

const promptForm = reactive<PromptForm>({
  title: '',
  content: '',
  description: '',
  category: 'general',
  status: 'active',
  tags: [],
  is_public: true,
})

const formRules = reactive({
  title: [
    { required: true, message: '请输入标题', trigger: 'blur' },
    { min: 1, max: 100, message: '标题长度在 1 到 100 个字符', trigger: 'blur' },
  ],
  content: [
    { required: true, message: '请输入提示词内容', trigger: 'blur' },
    { min: 1, max: 5000, message: '内容长度在 1 到 5000 个字符', trigger: 'blur' },
  ],
  category: [{ required: true, message: '请选择分类', trigger: 'change' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }],
})

const filteredPrompts = computed(() => {
  if (!promptsList.value || !Array.isArray(promptsList.value)) {
    return []
  }

  const filtered = promptsList.value.filter(prompt => {
    return prompt &&
           typeof prompt === 'object' &&
           prompt.prompt_id &&
           typeof prompt.title === 'string' &&
           typeof prompt.content === 'string'
  })

  // 如果有搜索关键词，进行搜索过滤
  if (searchKeyword.value && searchKeyword.value.trim()) {
    const keyword = searchKeyword.value.toLowerCase().trim()
    return filtered.filter(prompt => {
      return prompt.title.toLowerCase().includes(keyword) ||
             (prompt.description && prompt.description.toLowerCase().includes(keyword))
    })
  }

  return filtered
})

// 加载活跃提示词
const loadActivePrompt = async () => {
  try {
    console.log('🔍 正在加载活跃提示词...')
    const response = await apiService.prompts.getActivePrompt()
    console.log('📦 活跃提示词响应:', response)

    if (response?.status === 'success' && response?.data?.prompt_id) {
      activePromptId.value = response.data.prompt_id
      console.log('✅ 当前活跃提示词ID:', activePromptId.value)
    } else {
      activePromptId.value = null
      console.log('ℹ️ 暂无活跃提示词')
    }
  } catch (error: any) {
    console.error('❌ 加载活跃提示词失败:', error)
    activePromptId.value = null
    // 不显示错误消息，因为可能是首次使用
  }
}

const loadPrompts = async (searchQuery?: string) => {
  loading.value = true
  try {
    const params: any = {
      page: 1,
      page_size: 100,
    }

    if (searchQuery && searchQuery.trim()) {
      params.search = searchQuery.trim()
    }

    console.log('🔍 正在加载提示词列表，参数:', params)
    const response = await apiService.prompts.getPrompts(params)
    console.log('📦 API响应数据:', response)

    // 处理响应数据结构
    let promptsData: Prompt[] = []
    if (response?.data?.prompts) {
      // getPrompts返回包装格式: {data: {prompts: [...], total: ...}}
      promptsData = response.data.prompts
    } else if (response?.data && Array.isArray(response.data)) {
      // 直接返回数组
      promptsData = response.data
    } else if (Array.isArray(response)) {
      // 直接返回数组
      promptsData = response
    }

    promptsList.value = promptsData
    console.log('✅ 解析后的提示词列表:', promptsList.value)

    if (promptsList.value.length > 0) {
      ElMessage.success(`加载成功，找到 ${promptsList.value.length} 个提示词`)
    } else {
      ElMessage.info('暂无提示词数据')
    }
  } catch (error: any) {
    console.error('❌ 加载提示词列表失败:', error)
    promptsList.value = []
    ElMessage.error(`加载提示词列表失败: ${error.message || '未知错误'}`)
  } finally {
    loading.value = false
  }
}

const debouncedSearch = useDebounceFn(async () => {
  await loadPrompts(searchKeyword.value)
}, 300)

watch(searchKeyword, () => {
  debouncedSearch()
})

// 监听模式变化
watch(currentMode, (newMode, oldMode) => {
  console.log('🔄 模式变化:', { from: oldMode, to: newMode })
})

// 监听选中的提示词变化
watch(selectedPrompt, (newPrompt, oldPrompt) => {
  console.log('🔄 选中提示词变化:', {
    from: oldPrompt?.prompt_id || 'null',
    to: newPrompt?.prompt_id || 'null',
    newPrompt: newPrompt
  })
})

const handleSearch = () => {
  debouncedSearch()
}

const handleAddPrompt = () => {
  currentMode.value = 'add'
  Object.assign(promptForm, {
    title: '',
    content: '',
    description: '',
    category: 'general',
    status: 'active',
    tags: [],
    is_public: true,
  })
}

const handleSelectPrompt = async (prompt: Prompt) => {
  if (!prompt?.prompt_id) {
    ElMessage.error('提示词ID无效')
    return
  }

  try {
    console.log('🔍 获取提示词详情，ID:', prompt.prompt_id)

    // 调用API获取提示词详情 - request函数现在会自动提取data字段
    const promptData = await apiService.prompts.get(prompt.prompt_id)
    console.log('📦 提取的提示词数据:', promptData)

    if (promptData && promptData.prompt_id) {
      selectedPrompt.value = promptData
      currentMode.value = 'view'
      console.log('✅ 设置选中的提示词:', selectedPrompt.value)
      console.log('✅ 当前模式:', currentMode.value)
      ElMessage.success('提示词详情加载成功')
    } else {
      console.error('❌ 提示词数据无效:', promptData)
      ElMessage.error('提示词数据格式错误')
    }
  } catch (error: any) {
    console.error('❌ 获取提示词详情失败:', error)
    ElMessage.error('获取提示词详情失败: ' + (error.message || '未知错误'))
  }
}

const handleEditPrompt = () => {
  if (!selectedPrompt.value) {
    ElMessage.warning('请先选择一个提示词')
    return
  }

  const prompt = selectedPrompt.value
  Object.assign(promptForm, {
    title: prompt.title || '',
    content: prompt.content || '',
    description: prompt.description || '',
    category: prompt.category || 'general',
    status: prompt.status || 'active',
    tags: Array.isArray(prompt.tags) ? [...prompt.tags] : [],
    is_public: prompt.is_public !== undefined ? prompt.is_public : true,
  })

  currentMode.value = 'edit'
}

// 设置活跃提示词
const handleSetActivePrompt = async (prompt: Prompt) => {
  if (!prompt?.prompt_id) {
    ElMessage.error('提示词ID无效')
    return
  }

  if (settingActivePrompt.value) {
    return // 防止重复点击
  }

  try {
    settingActivePrompt.value = true
    console.log('🔄 正在设置活跃提示词:', prompt.prompt_id)

    const response = await apiService.prompts.setActivePrompt(prompt.prompt_id)
    console.log('📦 设置活跃提示词响应:', response)

    if (response?.status === 'success') {
      activePromptId.value = prompt.prompt_id
      ElMessage.success(`已将 "${prompt.title || '未命名提示词'}" 设置为默认提示词`)
      console.log('✅ 活跃提示词设置成功:', activePromptId.value)
    } else {
      ElMessage.error(response?.message || '设置默认提示词失败')
    }
  } catch (error: any) {
    console.error('❌ 设置活跃提示词失败:', error)
    ElMessage.error(`设置默认提示词失败: ${error.message || '未知错误'}`)
  } finally {
    settingActivePrompt.value = false
  }
}

const handleDeletePrompt = async (prompt: Prompt) => {
  if (!prompt?.prompt_id) {
    ElMessage.error('提示词ID无效')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除提示词 ${prompt.title || '未命名'} 吗？此操作不可恢复。`,
      '确认删除',
      {
        type: 'warning',
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
      },
    )

    await apiService.prompts.deletePrompt(prompt.prompt_id)
    ElMessage.success('提示词删除成功')

    // 如果删除的是活跃提示词，清除活跃状态
    if (activePromptId.value === prompt.prompt_id) {
      activePromptId.value = null
    }

    await loadPrompts()

    if (selectedPrompt.value?.prompt_id === prompt.prompt_id) {
      selectedPrompt.value = null
      currentMode.value = 'default'
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('删除提示词失败')
    }
  }
}

const handleSave = async () => {
  if (!promptFormRef.value) {
    ElMessage.error('表单引用无效')
    return
  }

  try {
    await promptFormRef.value.validate()
    saving.value = true

    if (currentMode.value === 'add') {
      await apiService.prompts.createPrompt(promptForm)
      ElMessage.success('提示词创建成功')
    } else if (currentMode.value === 'edit' && selectedPrompt.value?.prompt_id) {
      await apiService.prompts.updatePrompt(selectedPrompt.value.prompt_id, promptForm)
      ElMessage.success('提示词更新成功')
    }

    await loadPrompts()
    currentMode.value = 'default'
    selectedPrompt.value = null
    resetForm()
  } catch (error: any) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const handleCancel = () => {
  currentMode.value = selectedPrompt.value ? 'view' : 'default'
  resetForm()
}

const resetForm = () => {
  Object.assign(promptForm, {
    title: '',
    content: '',
    description: '',
    category: 'general',
    status: 'active',
    tags: [],
    is_public: true,
  })
  if (promptFormRef.value) {
    promptFormRef.value.clearValidate()
  }
}

const handleKeydown = (event: KeyboardEvent) => {
  if (event.ctrlKey && event.key === 'f') {
    event.preventDefault()
    searchInputRef.value?.focus()
  }
}

onMounted(async () => {
  document.addEventListener('keydown', handleKeydown)
  // 并行加载提示词列表和活跃提示词
  await Promise.all([
    loadPrompts(),
    loadActivePrompt()
  ])
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped lang="scss">
.ai-config-container {
  padding: 20px;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.page-header {
  margin-bottom: 20px;

  h2 {
    margin: 0 0 8px 0;
    color: #303133;
  }

  p {
    margin: 0;
    color: #606266;
    font-size: 14px;
  }
}

.main-content {
  flex: 1;
  display: flex;
  gap: 20px;
  min-height: 0;
}

.prompts-sidebar {
  width: 400px;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  overflow: hidden;
}

.sidebar-header {
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;

  h3 {
    margin: 0;
    font-size: 16px;
    color: #303133;
  }
}

.search-box {
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.prompts-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.prompt-item {
  padding: 12px;
  border-radius: 6px;
  cursor: pointer;
  margin-bottom: 8px;
  border: 1px solid transparent;
  display: flex;
  justify-content: space-between;
  align-items: center; /* 改为center，确保按钮列垂直居中对齐 */

  &:hover {
    background-color: #f5f7fa;
    border-color: #e4e7ed;
  }

  &.active {
    background-color: #ecf5ff;
    border-color: #409eff;
  }
}

.prompt-info {
  flex: 1;
  min-width: 0;
}

.prompt-title {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.prompt-desc {
  margin: 0 0 8px 0;
  font-size: 12px;
  color: #606266;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.prompt-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.usage-count {
  font-size: 12px;
  color: #909399;
}

.prompt-actions {
  display: grid;
  grid-template-rows: 25.6px 25.6px; /* 固定两行高度 */
  gap: 6px;
  margin-left: 12px;
  width: 25.6px; /* 固定宽度，确保所有按钮列对齐 */
  flex-shrink: 0; /* 防止容器被压缩 */
  justify-items: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */
}

.action-slot {
  width: 25.6px;
  height: 25.6px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.select-btn {
  width: 25.6px;  /* 缩小20%: 32px * 0.8 = 25.6px */
  height: 25.6px;
  padding: 0;
  background: #c8d4c8;  /* 浅灰绿色 - 未选择状态 */
  border: 1px solid #b8c4b8;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:hover {
    background: #b8c4b8;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  .el-icon {
    font-size: 12.8px;  /* 缩小20%: 16px * 0.8 = 12.8px */
    color: #6b7c6b;  /* 深灰绿色图标 */
  }
}

.active-btn {
  width: 25.6px;  /* 缩小20%: 32px * 0.8 = 25.6px */
  height: 25.6px;
  padding: 0;
  background: #5a7c5a;  /* 深绿色 - 选择状态 */
  border: 1px solid #4a6c4a;
  box-shadow: 0 2px 4px rgba(90, 124, 90, 0.3);
  position: relative;

  .el-icon {
    font-size: 12.8px;  /* 缩小20%: 16px * 0.8 = 12.8px */
    color: white;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
  }
}



.delete-btn {
  width: 25.6px;  /* 缩小20%: 32px * 0.8 = 25.6px */
  height: 25.6px;
  padding: 0;
  background: linear-gradient(135deg, #f56c6c 0%, #f78989 100%);
  border: none;
  box-shadow: 0 2px 4px rgba(245, 108, 108, 0.3);
  transition: all 0.3s ease;

  &:hover {
    background: linear-gradient(135deg, #f78989 0%, #f56c6c 100%);
    box-shadow: 0 4px 8px rgba(245, 108, 108, 0.4);
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(245, 108, 108, 0.3);
  }

  .el-icon {
    font-size: 12.8px;  /* 缩小20%: 16px * 0.8 = 12.8px */
    color: white;
  }
}

.detail-panel {
  flex: 1;
  background: #fff;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 确保flex子项能正确计算高度 */
}

/* 提示词内容编辑区域滚动支持 */
.prompt-content-textarea {
  :deep(.el-textarea__inner) {
    resize: vertical;
    overflow-y: auto;
    min-height: 200px;
    max-height: 400px;
    scrollbar-width: thin;
    scrollbar-color: #c1c1c1 #f1f1f1;

    /* Webkit浏览器滚动条样式 */
    &::-webkit-scrollbar {
      width: 8px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 4px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }
}

/* 提示词内容查看区域滚动支持 */
.prompt-content-display {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 12px;
  background-color: #f8f9fa;
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;

  /* Webkit浏览器滚动条样式 */
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;

    &:hover {
      background: #a8a8a8;
    }
  }

  pre {
    margin: 0;
    white-space: pre-wrap;
    word-wrap: break-word;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 14px;
    line-height: 1.5;
    color: #2c3e50;
  }
}

.detail-header, .form-header {
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;

  h3 {
    margin: 0;
    font-size: 16px;
    color: #303133;
  }
}

.detail-content, .form-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  min-height: 0; /* 确保flex子项能正确计算高度 */
}

.detail-section {
  margin-bottom: 24px;

  h4 {
    margin: 0 0 8px 0;
    font-size: 14px;
    color: #303133;
    font-weight: 500;
  }

  p {
    margin: 0;
    color: #606266;
    line-height: 1.6;
  }
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
}

.empty-prompts {
  padding: 20px;
  text-align: center;
}



/* 表单面板样式 */
.form-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 0;
}

.form-header {
  flex-shrink: 0; /* 防止头部被压缩 */
}

.form-content {
  flex: 1;
  min-height: 0;

  .el-form {
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .el-form-item {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.form-actions {
  padding: 16px;
  border-top: 1px solid #e4e7ed;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  flex-shrink: 0; /* 防止操作按钮被压缩 */
}
</style>
