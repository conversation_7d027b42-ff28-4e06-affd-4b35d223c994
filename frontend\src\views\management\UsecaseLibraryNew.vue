<template>
  <div class="usecase-library-container">

    <!-- 浏览器连接状态 -->
    <div class="browser-connection-section">
      <div class="browser-controls">
        <el-button
          v-if="!browserConnected"
          size="small"
          type="primary"
          :loading="connectingBrowser"
          @click="connectBrowser"
          class="browser-btn"
        >
          <el-icon><Link /></el-icon>
          连接浏览器
        </el-button>


        <el-button
          v-else
          size="small"
          type="danger"
          :loading="connectingBrowser"
          @click="disconnectBrowser"
          class="browser-btn"
        >
          <el-icon><Close /></el-icon>
          断开链接
        </el-button>
        <span v-if="browserConnected" class="connection-status">
          <el-icon class="connected-icon"><Check /></el-icon>
          浏览器已连接
        </span>



        <!-- 录制悬浮窗控制按钮 -->
        <el-button
          v-if="browserConnected"
          size="small"
          type="success"
          @click="toggleRecordingWindow"
          class="recording-btn"
        >
          <el-icon><Monitor /></el-icon>
          {{ floatingWindowOpen ? '隐藏' : '显示' }}录制悬浮窗
        </el-button>

        <el-button
          v-if="browserConnected && floatingWindowOpen"
          size="small"
          type="warning"
          @click="focusRecordingWindow"
          class="focus-btn"
        >
          <el-icon><Top /></el-icon>
          置顶悬浮窗
        </el-button>


      </div>
    </div>


    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 并排布局容器 -->
      <div class="libraries-container">
        <!-- 产品库区域 -->
        <div
          class="product-library-section"
          ref="productLibraryRef"
          :class="{ collapsed: productLibraryCollapsed }"
        >
          <!-- 折叠状态下的窄条 -->
          <div v-if="productLibraryCollapsed" class="collapsed-strip">
            <div class="expand-button" @click="toggleProductLibraryCollapse" title="展开产品库">
              <el-icon class="expand-icon">
                <Expand />
              </el-icon>
              <span class="strip-label">产品库</span>
            </div>
          </div>

          <!-- 正常状态下的内容 -->
          <template v-if="!productLibraryCollapsed">
            <div class="section-header">
              <h3>产品库 <span v-if="statistics" class="case-count">({{ statistics.productLibraryCases }}个用例)</span></h3>
              <div class="header-actions">
                <el-button
                  size="small"
                  circle
                  @click="toggleProductLibraryCollapse"
                  class="collapse-btn"
                  title="折叠产品库"
                >
                  <el-icon>
                    <Fold />
                  </el-icon>
                </el-button>
              </div>
            </div>
            <!-- 搜索区域 -->
            <div class="search-section">
              <div class="search-controls">
                <el-select
                  v-model="searchTarget"
                  placeholder="选择搜索范围"
                  size="small"
                  style="width: 150px; margin-right: 12px"
                >
                  <el-option label="树形目录" value="tree" />
                  <el-option label="表格区域" value="table" />
                </el-select>
                <el-input
                  v-model="searchKeyword"
                  placeholder="请输入搜索关键词..."
                  size="small"
                  style="width: 300px"
                  clearable
                  @input="handleSearch"
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
                <!-- 刷新按钮 -->
                <el-tooltip content="刷新用例数据" placement="top">
                  <el-button
                    type="primary"
                    size="small"
                    :icon="Refresh"
                    circle
                    @click="handleRefreshData"
                    :loading="isLoading"
                    style="margin-left: 12px"
                  />
                </el-tooltip>

              </div>
            </div>
            <!-- 版本管理区域 -->
            <div class="version-management-section">
              <div class="version-controls">
                <!-- 版本操作按钮 -->
                <div class="version-actions">
                  <el-tooltip content="上一个用例" placement="top">
                    <el-button
                      type="primary"
                      size="small"
                      :icon="ArrowLeft"
                      circle
                      @click="previousTestCase"
                      :disabled="!canGoPreviousTestCase"
                      class="version-btn nav-btn"
                    />
                  </el-tooltip>
                  <el-tooltip content="下一个用例" placement="top">
                    <el-button
                      type="primary"
                      size="small"
                      :icon="ArrowRight"
                      circle
                      @click="nextTestCase"
                      :disabled="!canGoNextTestCase"
                      class="version-btn nav-btn"
                    />
                  </el-tooltip>
                  <!-- 保存按钮 -->
                  <el-tooltip content="保存用例" placement="top">
                    <el-button
                      type="success"
                      size="small"
                      :icon="Check"
                      circle
                      @click="saveCaseDetail"
                      :disabled="isLoadingCaseDetail || isSavingCase || !selectedTestCase"
                      :loading="isSavingCase"
                      class="version-btn save-btn"
                    />
                  </el-tooltip>
                  <!-- 导入导出按钮 -->
                  <el-tooltip content="导入" placement="top">
                    <el-button
                      type="success"
                      size="small"
                      :icon="Upload"
                      circle
                      @click="importProduct"
                      class="version-btn import-btn"
                    />
                  </el-tooltip>
                  <el-tooltip content="导出" placement="top">
                    <el-button
                      type="warning"
                      size="small"
                      :icon="Download"
                      circle
                      @click="showExportPopover"
                      class="version-btn export-btn"
                    />
                  </el-tooltip>
                  <el-tooltip content="重置表单" placement="top">
                    <el-button
                      type="info"
                      size="small"
                      :icon="RefreshLeft"
                      circle
                      @click="resetForm"
                      :disabled="isLoadingCaseDetail || isSavingCase"
                      class="version-btn reset-btn"
                    />
                  </el-tooltip>
                </div>
                <!-- 版本选择器 - 只在选中测试用例时显示 -->
                <div
                  class="version-selector"
                  v-if="selectedTestCase && usecaseDetailForm.versions.length > 0"
                >
                  <span class="version-label">选择版本：</span>
                  <el-select
                    v-model="selectedVersionIndex"
                    @change="handleVersionChange"
                    class="version-select"
                    placeholder="请选择版本"
                  >
                    <el-option
                      v-for="(version, index) in usecaseDetailForm.versions"
                      :key="index"
                      :label="formatVersionDisplayText(version, index)"
                      :value="index"
                    >
                    </el-option>
                  </el-select>
                </div>
              </div>
            </div>
            <!-- 产品库内容区域 -->
            <div class="library-content">
              <!-- 左侧：产品树形结构 -->
              <div class="tree-section" :class="{ collapsed: productLibraryCollapsed }">
                <!-- 树形目录收起按钮 -->
                <div class="tree-collapse-button" @click="toggleTreeCollapse">
                  <el-icon>
                    <ArrowLeft v-if="!productLibraryCollapsed" />
                    <ArrowRight v-if="productLibraryCollapsed" />
                  </el-icon>
                </div>
                <div class="tree-content" v-if="!productLibraryCollapsed">
                  <el-tree
                    :data="filteredProductTree"
                    :props="treeProps"
                    node-key="id"
                    :default-expand-all="true"
                    @node-click="handleProductNodeClick"
                    class="product-tree"
                    :filter-node-method="filterTreeNode"
                    ref="productTreeRef"
                  >
                    <template #default="{ node, data }">
                      <div
                        class="tree-node-wrapper"
                        :class="{
                          selected: selectedTestCase?.id === data.id && data.type === 'testCase',
                          'drop-target':
                            (data.type === 'testCase' || data.type === 'testItem') &&
                            (dragState.isDragging || productDragState.isDragging),
                          'drag-over':
                            dragState.dragOverNode?.id === data.id ||
                            productDragState.dragOverNode?.id === data.id,
                          'product-drag-source': data.type === 'testCase',
                          'product-dragging':
                            productDragState.isDragging &&
                            productDragState.draggedNode?.id === data.id,
                        }"
                        @mouseenter="hoveredNodeId = data.id"
                        @mouseleave="hoveredNodeId = null"
                        @dragenter="handleProductTreeDragEnter($event, data)"
                        @dragover="handleProductTreeDragOver($event, data)"
                        @dragleave="handleProductTreeDragLeave"
                        @drop="handleProductTreeDrop($event, data)"
                        @mousedown="handleProductMouseDown($event, data)"
                        @mouseup="handleProductMouseUp"
                        @mousemove="handleProductMouseMove($event)"
                        :draggable="data.type === 'testCase'"
                        @dragstart="handleProductTreeDragStart($event, data)"
                        @dragend="handleProductTreeDragEnd"
                        :data-drop-position="
                          productDragState.dragOverNode?.id === data.id
                            ? productDragState.dropPosition
                            : ''
                        "
                      >
                        <span class="tree-node">
                          <el-icon v-if="data.type === 'system'" class="node-icon system-icon">
                            <Folder />
                          </el-icon>
                          <el-icon
                            v-else-if="data.type === 'function'"
                            class="node-icon function-icon"
                          >
                            <Grid />
                          </el-icon>
                          <el-icon
                            v-else-if="data.type === 'testItem'"
                            class="node-icon test-item-icon"
                          >
                            <Document />
                          </el-icon>
                          <el-icon
                            v-else-if="data.type === 'testCase'"
                            class="node-icon test-case-icon"
                          >
                            <Files />
                          </el-icon>
                          <!-- 内联编辑输入框 -->
                          <el-input
                            v-if="editingNodeId === data.id"
                            v-model="editingNodeName"
                            size="small"
                            class="inline-edit-input"
                            @keyup.enter="saveNodeName(data)"
                            @keyup.esc="cancelEdit"
                            @blur="saveNodeName(data)"
                            ref="editInputRef"
                          />
                          <!-- 正常显示 -->
                          <span
                            v-else
                            class="node-label"
                            :class="{ 'deleted-case': data.type === 'testCase' && data.deleted === true }"
                          >
                            {{ node.label }}
                          </span>
                        </span>

                        <!-- 悬停操作按钮 - 拖动时隐藏 -->
                        <div
                          v-if="hoveredNodeId === data.id && !productDragState.isDragging"
                          class="node-actions"
                        >
                          <el-button
                            size="small"
                            type="primary"
                            :icon="Plus"
                            @click.stop="addTreeNode(data, node)"
                            :title="getAddButtonTitle(data.type)"
                          />
                          <!-- 恢复按钮 - 只对deleted为true的用例显示 -->
                          <el-button
                            v-if="data.type === 'testCase' && data.deleted === true"
                            size="small"
                            type="success"
                            :icon="RefreshRight"
                            @click.stop="restoreCaseNode(data)"
                            title="恢复用例"
                          />
                          <!-- 删除按钮 - 只对deleted为false的用例显示 -->
                          <el-button
                            v-if="data.type !== 'testCase' || data.deleted !== true"
                            size="small"
                            type="danger"
                            :icon="Delete"
                            @click.stop="data.type === 'testCase' ? deleteCaseNode(data) : deleteTreeNode(data, node)"
                            title="删除"
                          />
                        </div>
                      </div>
                    </template>
                  </el-tree>
                </div>
              </div>
              <!-- 右侧：用例详情表单 -->
              <div class="usecase-detail-section" v-if="selectedTestCase" ref="usecaseDetailRef">
                <!-- 加载状态 -->
                <div v-if="isLoadingCaseDetail" class="loading-state">
                  <el-skeleton :rows="8" animated />
                  <div class="loading-text">正在加载用例详情...</div>
                </div>
                <!-- 用例详情表单 -->
                <div v-else class="usecase-detail-content">
                  <el-form :model="usecaseDetailForm" label-width="80px" size="small">
                    <!-- 第一行：用例名称和标识 -->
                    <div class="form-row">
                      <el-form-item label="用例名称" class="form-item-half">
                        <el-input
                          v-model="usecaseDetailForm.caseName"
                          placeholder="请输入"
                          @input="handleTestCaseNameChange"
                        />
                      </el-form-item>
                      <el-form-item label="标识" class="form-item-half">
                        <el-input
                          v-model="usecaseDetailForm.identifier"
                          placeholder="请选择"
                          readonly
                          class="readonly-input"
                        />
                      </el-form-item>
                    </div>

                    <!-- 追踪关系 -->
                    <el-form-item label="追踪关系">
                      <el-input
                        v-model="usecaseDetailForm.traceability"
                        type="textarea"
                        :autosize="true"
                        placeholder="请输入追踪关系"
                      />
                    </el-form-item>

                    <!-- 用例综述 -->
                    <el-form-item label="用例综述">
                      <el-input
                        v-model="usecaseDetailForm.summary"
                        type="textarea"
                        :autosize="true"
                        placeholder="请输入用例综述"
                      />
                    </el-form-item>

                    <!-- 初始化 -->
                    <el-form-item label="初始化">
                      <el-input
                        v-model="usecaseDetailForm.initialization"
                        type="textarea"
                        :autosize="true"
                        placeholder="请输入初始化条件"
                      />
                    </el-form-item>

                    <!-- 前提约束 -->
                    <el-form-item label="前提约束">
                      <el-input
                        v-model="usecaseDetailForm.prerequisites"
                        type="textarea"
                        :autosize="true"
                        placeholder="请输入前提约束"
                      />
                    </el-form-item>

                    <!-- 测试方法 -->
                    <el-form-item label="测试方法">
                      <el-input
                        v-model="usecaseDetailForm.testMethod"
                        type="textarea"
                        :autosize="true"
                        placeholder="请输入测试方法"
                      />
                    </el-form-item>

                    <!-- 测试步骤表格 -->
                    <el-form-item label="测试步骤">
                      <div class="test-steps-table">
                        <!-- 操作按钮 -->
                        <div class="table-actions">
                          <el-button
                            size="small"
                            type="primary"
                            :icon="Plus"
                            circle
                            @click="addTestStep"
                            class="action-btn add-btn"
                            title="添加测试步骤"
                          />
                          <el-button
                            size="small"
                            type="danger"
                            :icon="Minus"
                            circle
                            @click="removeTestStep"
                            :disabled="!canRemoveStep"
                            class="action-btn remove-btn"
                            title="删除选中步骤"
                          />
                        </div>

                        <el-table
                          :data="usecaseDetailForm.testSteps"
                          border
                          size="small"
                          class="test-steps-table-content"
                          style="width: 100%"
                          table-layout="fixed"
                        >
                          <el-table-column label="" width="50" align="center" resizable>
                            <template #default="{ row, $index }">
                              <el-radio
                                v-model="selectedRowIndex"
                                :label="$index"
                                size="small"
                                @click="handleRadioClick($index)"
                              >
                                <span></span>
                              </el-radio>
                            </template>
                          </el-table-column>

                          <el-table-column
                            prop="stepNo"
                            label="序号"
                            width="50"
                            align="center"
                            resizable
                          />

                          <el-table-column
                            prop="inputAction"
                            label="输入及操作"
                            width="250"
                            resizable
                          >
                            <template #default="{ row }">
                              <el-input
                                v-model="row.inputAction"
                                type="textarea"
                                :autosize="true"
                                size="small"
                              />
                            </template>
                          </el-table-column>

                          <el-table-column
                            prop="expectedResult"
                            label="预期结果"
                            width="250"
                            resizable
                          >
                            <template #default="{ row }">
                              <el-input
                                v-model="row.expectedResult"
                                type="textarea"
                                :autosize="true"
                                size="small"
                              />
                            </template>
                          </el-table-column>

                          <el-table-column
                            prop="actualResult"
                            label="实测结果"
                            width="250"
                            resizable
                          >
                            <template #default="{ row }">
                              <el-input
                                v-model="row.actualResult"
                                type="textarea"
                                :autosize="true"
                                size="small"
                              />
                            </template>
                          </el-table-column>

                          <el-table-column
                            prop="evaluationCriteria"
                            label="评估标准"
                            width="180"
                            resizable
                          >
                            <template #default="{ row }">
                              <el-input
                                v-model="row.evaluationCriteria"
                                type="textarea"
                                :autosize="true"
                                size="small"
                              />
                            </template>
                          </el-table-column>

                          <el-table-column
                            prop="testConclusion"
                            label="测试结论"
                            width="120"
                            resizable
                          >
                            <template #default="{ row }">
                              <el-input
                                v-model="row.testConclusion"
                                type="textarea"
                                :autosize="true"
                                size="small"
                              />
                            </template>
                          </el-table-column>
                        </el-table>
                      </div>
                    </el-form-item>

                    <!-- 截图展示区域 -->
                    <el-form-item label="测试截图">
                      <div class="screenshot-gallery">
                        <div class="screenshot-container">
                          <div
                            v-for="(screenshot, index) in sortedProductScreenshots"
                            :key="screenshot.id"
                            class="screenshot-item"
                            :class="{
                              selected: screenshotState.productSelectedIndices.includes(
                                getActualScreenshotIndex(index, 'product'),
                              ),
                              unselected:
                                screenshotState.productSelectedIndices.length > 0 &&
                                !screenshotState.productSelectedIndices.includes(
                                  getActualScreenshotIndex(index, 'product'),
                                ),
                              dragging: screenshotState.productDraggedIndex === index,
                              'drag-over': screenshotState.productDragOverIndex === index,
                            }"
                            :draggable="
                              screenshotState.productSelectedIndices.includes(
                                getActualScreenshotIndex(index, 'product'),
                              )
                            "
                            @click="handleProductImageClick(index)"
                            @mouseenter="screenshotState.productHoveredIndex = index"
                            @mouseleave="screenshotState.productHoveredIndex = -1"
                            @dragstart="handleProductDragStart(index, $event)"
                            @dragover="handleProductDragOver(index, $event)"
                            @drop="handleProductDrop(index, $event)"
                            @dragend="handleProductDragEnd"
                          >
                            <div class="screenshot-wrapper">
                              <img
                                :src="screenshot.thumbnail"
                                :alt="screenshot.title"
                                class="screenshot-image"
                                @error="
                                  ($event.target as HTMLImageElement).src =
                                    'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTUwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDE1MCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxNTAiIGhlaWdodD0iMTAwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik03NSA0MEw4NSA1MEg2NUw3NSA0MFoiIGZpbGw9IiNEOUQ5RDkiLz4KPHN2ZyB3aWR0aD0iMTUwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDE1MCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxNTAiIGhlaWdodD0iMTAwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik03NSA0MEw4NSA1MEg2NUw3NSA0MFoiIGZpbGw9IiNEOUQ5RDkiLz4KPC9zdmc+'
                                "
                              />
                              <div class="screenshot-overlay">
                                <div class="screenshot-title">{{ screenshot.title }}</div>
                                <div class="screenshot-timestamp">{{ screenshot.timestamp }}</div>
                              </div>
                              <!-- 悬停时显示选择按钮，已选中时显示对勾 -->
                              <div
                                v-if="
                                  screenshotState.productSelectedIndices.includes(
                                    getActualScreenshotIndex(index, 'product'),
                                  )
                                "
                                class="selected-icon"
                                @click.stop="handleProductToggleSelect(index)"
                              >
                                <el-icon><Check /></el-icon>
                              </div>
                              <div
                                v-else-if="screenshotState.productHoveredIndex === index"
                                class="select-button"
                                @click.stop="handleProductToggleSelect(index)"
                              >
                                <el-icon><Plus /></el-icon>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </el-form-item>

                    <!-- 终止条件 -->
                    <el-form-item label="终止条件">
                      <el-input
                        v-model="usecaseDetailForm.terminationCriteria"
                        type="textarea"
                        :autosize="true"
                        placeholder="请输入终止条件"
                        size="small"
                      />
                    </el-form-item>

                    <!-- 通过准则 -->
                    <el-form-item label="通过准则">
                      <el-input
                        v-model="usecaseDetailForm.acceptanceCriteria"
                        type="textarea"
                        :autosize="true"
                        placeholder="请输入通过准则"
                        size="small"
                      />
                    </el-form-item>

                    <!-- 执行状态和执行结果 -->
                    <div class="form-row">
                      <el-form-item label="执行状态" class="form-item-half">
                        <el-input
                          v-model="usecaseDetailForm.executionStatus"
                          placeholder="请输入执行状态"
                          size="small"
                        />
                      </el-form-item>
                      <el-form-item label="执行结果" class="form-item-half">
                        <el-input
                          v-model="usecaseDetailForm.executionResult"
                          placeholder="请输入执行结果"
                          size="small"
                        />
                      </el-form-item>
                    </div>

                    <!-- 问题单标识 -->
                    <el-form-item label="问题单标识">
                      <el-input
                        v-model="usecaseDetailForm.issueId"
                        placeholder="请输入问题单标识"
                        size="small"
                      />
                    </el-form-item>

                    <!-- 设计人员和监测人员 -->
                    <div class="form-row">
                      <el-form-item label="设计人员" class="form-item-half">
                        <el-input
                          v-model="usecaseDetailForm.designer"
                          placeholder="请输入设计人员"
                          size="small"
                        />
                      </el-form-item>
                      <el-form-item label="监测人员" class="form-item-half">
                        <el-input
                          v-model="usecaseDetailForm.reviewer"
                          placeholder="请输入监测人员"
                          size="small"
                        />
                      </el-form-item>
                    </div>

                    <!-- 测试时间 -->
                    <el-form-item label="测试时间">
                      <el-date-picker
                        v-model="productTestDate"
                        type="date"
                        placeholder="请选择测试时间"
                        size="small"
                        format="YYYY.MM.DD"
                        value-format="YYYY-MM-DD"
                        @change="handleProductTestDateChange"
                        style="width: 100%"
                      />
                    </el-form-item>


                  </el-form>
                </div>
              </div>
              <!-- 右侧空状态 -->
              <div class="usecase-detail-section" v-else>
                <div class="empty-state">
                  <el-empty description="请选择一个测试用例查看详情" />
                </div>
              </div>
            </div>

            <!-- 导出系统选择对话框 -->
            <el-dialog
              v-model="exportDialogVisible"
              title="选择要导出的系统"
              width="400px"
              center
              :close-on-click-modal="false"
            >
              <div class="export-dialog-content">
                <el-checkbox-group v-model="selectedExportSystems" class="export-checkbox-group">
                  <el-checkbox
                    v-for="system in availableSystems"
                    :key="system.id"
                    :label="system.id"
                    class="export-checkbox"
                  >
                    {{ system.name }}
                  </el-checkbox>
                </el-checkbox-group>
              </div>
              <template #footer>
                <div class="export-actions">
                  <el-button @click="exportDialogVisible = false">取消</el-button>
                  <el-button type="primary" @click="confirmExport">确定导出</el-button>
                </div>
              </template>
            </el-dialog>
          </template>
        </div>
        <!-- 产品库内容区域结束 -->
        <!-- 备选库区域 -->
        <div
          class="candidate-library-section"
          ref="candidateLibraryRef"
          :class="{ collapsed: candidateLibraryCollapsed }"
        >
          <!-- 折叠状态下的窄条 -->
          <div v-if="candidateLibraryCollapsed" class="collapsed-strip">
            <div class="expand-button" @click="toggleCandidateLibraryCollapse" title="展开备选库">
              <el-icon class="expand-icon">
                <Expand />
              </el-icon>
              <span class="strip-label">备选库</span>
            </div>
          </div>

          <!-- 正常状态下的内容 -->
          <template v-if="!candidateLibraryCollapsed">
            <div class="section-header">
              <h3>备选库 <span v-if="statistics" class="case-count">({{ statistics.candidateLibraryCases }}个用例)</span></h3>
              <div class="header-actions">
                <el-button
                  size="small"
                  circle
                  @click="toggleCandidateLibraryCollapse"
                  class="collapse-btn"
                  title="折叠备选库"
                >
                  <el-icon>
                    <Fold />
                  </el-icon>
                </el-button>
              </div>
            </div>
            <!-- 搜索区域 -->
            <div class="search-section">
              <div class="search-controls">
                <el-select
                  v-model="candidateSearchTarget"
                  placeholder="选择搜索范围"
                  size="small"
                  style="width: 150px; margin-right: 12px"
                >
                  <el-option label="树形目录" value="tree" />
                  <el-option label="表格区域" value="table" />
                </el-select>
                <el-input
                  v-model="candidateSearchKeyword"
                  placeholder="请输入搜索关键词..."
                  size="small"
                  style="width: 300px"
                  clearable
                  @input="handleCandidateSearch"
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
              </div>
            </div>
            <!-- 版本管理区域 -->
            <div class="version-management-section">
              <div class="version-controls">
                <!-- 版本导航按钮 -->
                <div class="version-actions">
                  <el-tooltip content="上一个用例" placement="top">
                    <el-button
                      type="primary"
                      size="small"
                      :icon="ArrowLeft"
                      circle
                      @click="previousCandidateTestCase"
                      :disabled="!canGoPreviousCandidateTestCase"
                      class="version-btn nav-btn"
                    />
                  </el-tooltip>
                  <el-tooltip content="下一个用例" placement="top">
                    <el-button
                      type="primary"
                      size="small"
                      :icon="ArrowRight"
                      circle
                      @click="nextCandidateTestCase"
                      :disabled="!canGoNextCandidateTestCase"
                      class="version-btn nav-btn"
                    />
                  </el-tooltip>
                </div>
              </div>
            </div>

            <!-- 备选库内容区域 -->
            <div class="library-content">
              <!-- 左侧：备选库树形结构 -->
              <div class="tree-section" :class="{ collapsed: candidateLibraryCollapsed }">
                <!-- 树形目录收起按钮 -->
                <div class="tree-collapse-button" @click="toggleCandidateTreeCollapse">
                  <el-icon>
                    <ArrowLeft v-if="!candidateLibraryCollapsed" />
                    <ArrowRight v-if="candidateLibraryCollapsed" />
                  </el-icon>
                </div>
                <div class="tree-content" v-if="!candidateLibraryCollapsed">
                  <el-tree
                    :data="filteredCandidateTree"
                    :props="treeProps"
                    node-key="id"
                    :default-expand-all="true"
                    @node-click="handleCandidateNodeClick"
                    class="product-tree"
                    :filter-node-method="filterTreeNode"
                    ref="candidateTreeRef"
                  >
                    <template #default="{ node, data }">
                      <div
                        class="tree-node-wrapper"
                        :class="{
                          selected:
                            selectedCandidateTestCase?.id === data.id && data.type === 'testCase',
                          'drag-source': data.type === 'testCase',
                          dragging: dragState.isDragging && dragState.draggedNode?.id === data.id,
                        }"
                        @mouseenter="candidateHoveredNodeId = data.id"
                        @mouseleave="candidateHoveredNodeId = null"
                        @mousedown="data.type === 'testCase' ? handleMouseDown($event, data) : null"
                        @mouseup="handleMouseUp"
                        @mousemove="handleMouseMove"
                      >
                        <span class="tree-node">
                          <!-- 测试用例的向左箭头（移动到产品库） -->
                          <el-button
                            v-if="data.type === 'testCase' && candidateHoveredNodeId === data.id"
                            size="small"
                            type="primary"
                            class="move-to-product-btn"
                            @click.stop="moveCaseToProduct(data)"
                            title="移动到产品库"
                          >
                            <el-icon>
                              <ArrowLeft />
                            </el-icon>
                          </el-button>
                          <el-icon v-if="data.type === 'system'" class="node-icon system-icon">
                            <Folder />
                          </el-icon>
                          <el-icon
                            v-else-if="data.type === 'function'"
                            class="node-icon function-icon"
                          >
                            <Grid />
                          </el-icon>
                          <el-icon
                            v-else-if="data.type === 'testItem'"
                            class="node-icon test-item-icon"
                          >
                            <Document />
                          </el-icon>
                          <el-icon
                            v-else-if="data.type === 'testCase'"
                            class="node-icon test-case-icon"
                          >
                            <Files />
                          </el-icon>
                          <!-- 默认路径选中图标 -->
                          <el-icon
                            v-if="data.type === 'testItem' && selectedDefaultPath &&
                                  selectedDefaultPath.tableName === data.tableName &&
                                  selectedDefaultPath.testMethod === data.name"
                            class="node-icon selected-icon"
                            title="已设置为默认用例路径"
                          >
                            <Pointer />
                          </el-icon>
                          <!-- 内联编辑输入框 -->
                          <el-input
                            v-if="editingNodeId === data.id"
                            v-model="editingNodeName"
                            size="small"
                            class="inline-edit-input"
                            @keyup.enter="saveNodeName(data)"
                            @keyup.esc="cancelEdit"
                            @blur="saveNodeName(data)"
                            ref="editInputRef"
                          />
                          <!-- 正常显示 -->
                          <span
                            v-else
                            class="node-label"
                            :class="{ 'deleted-case': data.type === 'testCase' && data.deleted === true }"
                          >
                            {{ node.label }}
                          </span>
                        </span>
                        <!-- 悬停操作按钮 -->
                        <div v-if="candidateHoveredNodeId === data.id" class="node-actions">
                          <!-- 备选库不可以新增用例，只允许添加功能和测试项 -->
                          <el-button
                            v-if="data.type !== 'testItem' && data.type !== 'testCase'"
                            size="small"
                            type="primary"
                            :icon="Plus"
                            @click.stop="addCandidateTreeNode(data, node)"
                            :title="getAddButtonTitle(data.type)"
                          />
                          <el-button
                            size="small"
                            type="danger"
                            :icon="Delete"
                            @click.stop="deleteCandidateTreeNode(data, node)"
                            title="删除"
                          />
                        </div>
                      </div>
                    </template>
                  </el-tree>
                </div>
              </div>
              <!-- 左侧备选库树形结构区域结束 -->
              <!-- 右侧：备选库用例详情表单 -->
              <div
                class="usecase-detail-section"
                v-if="selectedCandidateTestCase"
                ref="candidateDetailRef"
              >
                <div class="usecase-detail-content">
                  <el-form :model="candidateDetailForm" label-width="80px" size="small">
                    <!-- 第一行：用例名称和标识 -->
                    <div class="form-row">
                      <el-form-item label="用例名称" class="form-item-half">
                        <el-input
                          v-model="candidateDetailForm.testCaseName"
                          placeholder="请输入"
                          @input="handleCandidateTestCaseNameChange"
                        />
                      </el-form-item>
                      <el-form-item label="标识" class="form-item-half">
                        <el-input
                          v-model="candidateDetailForm.identifier"
                          placeholder="请选择"
                          readonly
                          class="readonly-input"
                        />
                      </el-form-item>
                    </div>
                    <!-- 追踪关系 -->
                    <el-form-item label="追踪关系">
                      <el-input
                        v-model="candidateDetailForm.traceRelation"
                        type="textarea"
                        :autosize="true"
                        placeholder=""
                      />
                    </el-form-item>
                    <!-- 用例综述 -->
                    <el-form-item label="用例综述">
                      <el-input
                        v-model="candidateDetailForm.testCaseSummary"
                        type="textarea"
                        :autosize="true"
                        placeholder=""
                      />
                    </el-form-item>
                    <!-- 初始化 -->
                    <el-form-item label="初始化">
                      <el-input
                        v-model="candidateDetailForm.usecaseInitialization"
                        type="textarea"
                        :autosize="true"
                        placeholder=""
                      />
                    </el-form-item>
                    <!-- 前提约束 -->
                    <el-form-item label="前提约束">
                      <el-input
                        v-model="candidateDetailForm.prerequisitesConstraints"
                        type="textarea"
                        :autosize="true"
                        placeholder=""
                      />
                    </el-form-item>
                    <!-- 测试方法 -->
                    <el-form-item label="测试方法">
                      <el-input
                        v-model="candidateDetailForm.testMethod"
                        type="textarea"
                        :autosize="true"
                        placeholder=""
                      />
                    </el-form-item>
                    <!-- 测试步骤表格 -->
                    <el-form-item label="测试步骤">
                      <div class="test-steps-table">
                        <!-- 操作按钮 -->
                        <div class="table-actions">
                          <el-button
                            size="small"
                            type="primary"
                            :icon="Plus"
                            circle
                            @click="addCandidateTestStep"
                            class="action-btn add-btn"
                            title="添加测试步骤"
                          />
                          <el-button
                            size="small"
                            type="danger"
                            :icon="Minus"
                            circle
                            @click="removeCandidateTestStep"
                            :disabled="!candidateCanRemoveStep"
                            class="action-btn remove-btn"
                            title="删除选中步骤"
                          />
                        </div>

                        <el-table
                          :data="candidateDetailForm.testSteps"
                          border
                          size="small"
                          class="test-steps-table-content"
                          style="width: 100%"
                          table-layout="fixed"
                        >
                          <el-table-column label="" width="50" align="center" resizable>
                            <template #default="{ row, $index }">
                              <el-radio
                                v-model="candidateSelectedRowIndex"
                                :label="$index"
                                size="small"
                                @click="handleCandidateRadioClick($index)"
                              >
                                <span></span>
                              </el-radio>
                            </template>
                          </el-table-column>

                          <el-table-column
                            prop="sequence"
                            label="序号"
                            width="50"
                            align="center"
                            resizable
                          />

                          <el-table-column
                            prop="inputOperation"
                            label="输入及操作"
                            width="250"
                            resizable
                          >
                            <template #default="{ row }">
                              <el-input
                                v-model="row.inputOperation"
                                type="textarea"
                                :autosize="true"
                                size="small"
                              />
                            </template>
                          </el-table-column>

                          <el-table-column
                            prop="expectedResult"
                            label="预期结果"
                            width="250"
                            resizable
                          >
                            <template #default="{ row }">
                              <el-input
                                v-model="row.expectedResult"
                                type="textarea"
                                :autosize="true"
                                size="small"
                              />
                            </template>
                          </el-table-column>

                          <el-table-column
                            prop="actualResult"
                            label="实测结果"
                            width="250"
                            resizable
                          >
                            <template #default="{ row }">
                              <el-input
                                v-model="row.actualResult"
                                type="textarea"
                                :autosize="true"
                                size="small"
                              />
                            </template>
                          </el-table-column>

                          <el-table-column
                            prop="evaluationCriteria"
                            label="评估标准"
                            width="180"
                            resizable
                          >
                            <template #default="{ row }">
                              <el-input
                                v-model="row.evaluationCriteria"
                                type="textarea"
                                :autosize="true"
                                size="small"
                              />
                            </template>
                          </el-table-column>

                          <el-table-column
                            prop="testConclusion"
                            label="测试结论"
                            width="120"
                            resizable
                          >
                            <template #default="{ row }">
                              <el-input
                                v-model="row.testConclusion"
                                type="textarea"
                                :autosize="true"
                                size="small"
                              />
                            </template>
                          </el-table-column>
                        </el-table>
                      </div>
                    </el-form-item>

                    <!-- 截图展示区域 -->
                    <el-form-item label="测试截图">
                      <div class="screenshot-gallery">
                        <div class="screenshot-container">
                          <div
                            v-for="(screenshot, index) in sortedCandidateScreenshots"
                            :key="screenshot.id"
                            class="screenshot-item"
                            :class="{
                              selected: screenshotState.candidateSelectedIndices.includes(
                                getActualScreenshotIndex(index, 'candidate'),
                              ),
                              unselected:
                                screenshotState.candidateSelectedIndices.length > 0 &&
                                !screenshotState.candidateSelectedIndices.includes(
                                  getActualScreenshotIndex(index, 'candidate'),
                                ),
                              dragging: screenshotState.candidateDraggedIndex === index,
                              'drag-over': screenshotState.candidateDragOverIndex === index,
                            }"
                            :draggable="
                              screenshotState.candidateSelectedIndices.includes(
                                getActualScreenshotIndex(index, 'candidate'),
                              )
                            "
                            @click="handleCandidateImageClick(index)"
                            @mouseenter="screenshotState.candidateHoveredIndex = index"
                            @mouseleave="screenshotState.candidateHoveredIndex = -1"
                            @dragstart="handleCandidateDragStart(index, $event)"
                            @dragover="handleCandidateDragOver(index, $event)"
                            @drop="handleCandidateDrop(index, $event)"
                            @dragend="handleCandidateDragEnd"
                          >
                            <div class="screenshot-wrapper">
                              <img
                                :src="screenshot.thumbnail"
                                :alt="screenshot.title"
                                class="screenshot-image"
                                @error="
                                  ($event.target as HTMLImageElement).src =
                                    'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTUwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDE1MCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxNTAiIGhlaWdodD0iMTAwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik03NSA0MEw4NSA1MEg2NUw3NSA0MFoiIGZpbGw9IiNEOUQ5RDkiLz4KPHN2ZyB3aWR0aD0iMTUwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDE1MCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxNTAiIGhlaWdodD0iMTAwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik03NSA0MEw4NSA1MEg2NUw3NSA0MFoiIGZpbGw9IiNEOUQ5RDkiLz4KPC9zdmc+'
                                "
                              />
                              <div class="screenshot-overlay">
                                <div class="screenshot-title">{{ screenshot.title }}</div>
                                <div class="screenshot-timestamp">{{ screenshot.timestamp }}</div>
                              </div>
                              <!-- 悬停时显示选择按钮，已选中时显示对勾 -->
                              <div
                                v-if="
                                  screenshotState.candidateSelectedIndices.includes(
                                    getActualScreenshotIndex(index, 'candidate'),
                                  )
                                "
                                class="selected-icon"
                                @click.stop="handleCandidateToggleSelect(index)"
                              >
                                <el-icon><Check /></el-icon>
                              </div>
                              <div
                                v-else-if="screenshotState.candidateHoveredIndex === index"
                                class="select-button"
                                @click.stop="handleCandidateToggleSelect(index)"
                              >
                                <el-icon><Plus /></el-icon>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </el-form-item>

                    <!-- 终止条件 -->
                    <el-form-item label="终止条件">
                      <el-input
                        v-model="candidateDetailForm.testCaseTerminationCondition"
                        type="textarea"
                        :autosize="true"
                        placeholder="请输入终止条件"
                        size="small"
                      />
                    </el-form-item>

                    <!-- 通过准则 -->
                    <el-form-item label="通过准则">
                      <el-input
                        v-model="candidateDetailForm.testCasePassCriteria"
                        type="textarea"
                        :autosize="true"
                        placeholder="请输入通过准则"
                        size="small"
                      />
                    </el-form-item>

                    <!-- 执行状态和执行结果 -->
                    <div class="form-row">
                      <el-form-item label="执行状态" class="form-item-half">
                        <el-input
                          v-model="candidateDetailForm.executionStatus"
                          placeholder="请输入执行状态"
                          size="small"
                        />
                      </el-form-item>
                      <el-form-item label="执行结果" class="form-item-half">
                        <el-input
                          v-model="candidateDetailForm.executionResult"
                          placeholder="请输入执行结果"
                          size="small"
                        />
                      </el-form-item>
                    </div>

                    <!-- 问题单标识 -->
                    <el-form-item label="问题单标识">
                      <el-input
                        v-model="candidateDetailForm.issueIdentification"
                        placeholder="请输入问题单标识"
                        size="small"
                      />
                    </el-form-item>

                    <!-- 设计人员和监测人员 -->
                    <div class="form-row">
                      <el-form-item label="设计人员" class="form-item-half">
                        <el-input
                          v-model="candidateDetailForm.designer"
                          placeholder="请输入设计人员"
                          size="small"
                        />
                      </el-form-item>
                      <el-form-item label="监测人员" class="form-item-half">
                        <el-input
                          v-model="candidateDetailForm.reviewer"
                          placeholder="请输入监测人员"
                          size="small"
                        />
                      </el-form-item>
                    </div>

                    <!-- 测试时间 -->
                    <el-form-item label="测试时间">
                      <el-date-picker
                        v-model="candidateTestDate"
                        type="date"
                        placeholder="请选择测试时间"
                        size="small"
                        format="YYYY.MM.DD"
                        value-format="YYYY-MM-DD"
                        @change="handleCandidateTestDateChange"
                        style="width: 100%"
                      />
                    </el-form-item>

                    <!-- 测试结果 -->
                    <el-form-item label="测试结果">
                      <el-input
                        v-model="candidateDetailForm.testResult"
                        type="textarea"
                        :autosize="true"
                        placeholder=""
                      />
                    </el-form-item>
                  </el-form>
                </div>
              </div>
              <!-- 右侧空状态 -->
              <div class="usecase-detail-section" v-else>
                <div class="empty-state">
                  <el-empty description="请选择一个测试用例查看详情" />
                </div>
              </div>
            </div>

            <!-- 添加附件对话框 -->
            <el-dialog v-model="attachmentDialogVisible" title="添加附件" width="500px">
              <el-form :model="newAttachment" label-width="80px">
                <el-form-item label="附件名称">
                  <el-input v-model="newAttachment.name" placeholder="请输入附件名称" />
                </el-form-item>
                <el-form-item label="附件文件">
                  <el-upload
                    ref="uploadRef"
                    :auto-upload="false"
                    :on-change="handleFileChange"
                    :file-list="fileList"
                  >
                    <el-button type="primary">选择文件</el-button>
                  </el-upload>
                </el-form-item>
              </el-form>
              <template #footer>
                <el-button @click="attachmentDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="confirmAddAttachment">确定</el-button>
              </template>
            </el-dialog>
          </template>
        </div>
      </div>
    </div>

    <!-- 产品库截图模态框 -->
    <el-dialog
      v-model="screenshotState.productModalVisible"
      title="截图预览"
      width="90%"
      :before-close="closeProductModal"
      class="screenshot-modal"
      @keydown.esc="closeProductModal"
      @keydown.left="previousProductImage"
      @keydown.right="nextProductImage"
    >
      <!-- 模态框头部信息 -->
      <template #header>
        <div class="modal-header">
          <span class="modal-title">截图预览</span>
          <div class="modal-info">
            <!-- 选择按钮移动到位置信息左侧 -->
            <div
              class="modal-select-button"
              :class="{ selected: isProductImageSelected() }"
              @click="toggleProductImageSelection"
            >
              <el-icon><Check v-if="isProductImageSelected()" /><Plus v-else /></el-icon>
            </div>
            <span class="image-position"
              >{{ screenshotState.productDisplayIndex + 1 }} /
              {{ sortedProductScreenshots.length }}</span
            >
            <span class="image-scale"
              >{{ Math.round(screenshotState.productImageScale * 100) }}%</span
            >
          </div>
        </div>
      </template>

      <div class="modal-content">
        <!-- 左侧导航按钮 -->
        <button
          class="nav-button nav-button-left"
          :disabled="screenshotState.productDisplayIndex === 0"
          @click="previousProductImage"
        >
          <el-icon><ArrowLeft /></el-icon>
        </button>

        <!-- 图片容器 -->
        <div
          class="modal-image-container"
          @click="closeProductModal"
          @wheel.prevent="handleProductImageWheel"
        >
          <img
            :src="screenshotState.productCurrentImage"
            alt="截图预览"
            class="modal-image"
            :style="{ transform: `scale(${screenshotState.productImageScale})` }"
            @click.stop
          />
        </div>

        <!-- 右侧导航按钮 -->
        <button
          class="nav-button nav-button-right"
          :disabled="screenshotState.productDisplayIndex === sortedProductScreenshots.length - 1"
          @click="nextProductImage"
        >
          <el-icon><ArrowRight /></el-icon>
        </button>
      </div>

      <!-- 底部控制栏 -->
      <template #footer>
        <div class="modal-footer">
          <div class="modal-controls-center">
            <!-- 缩放控制 -->
            <el-button-group size="small">
              <el-button
                @click="zoomProductImage('out')"
                :disabled="screenshotState.productImageScale <= 0.5"
              >
                <el-icon><ZoomOut /></el-icon>
              </el-button>
              <el-button @click="resetProductImageScale"> 重置 </el-button>
              <el-button
                @click="zoomProductImage('in')"
                :disabled="screenshotState.productImageScale >= 2"
              >
                <el-icon><ZoomIn /></el-icon>
              </el-button>
            </el-button-group>
          </div>

          <div class="modal-controls-right">
            <el-button size="small" @click="closeProductModal">关闭</el-button>
          </div>
        </div>
      </template>
    </el-dialog>

    <!-- 备选库截图模态框 -->
    <el-dialog
      v-model="screenshotState.candidateModalVisible"
      title="截图预览"
      width="90%"
      :before-close="closeCandidateModal"
      class="screenshot-modal"
      @keydown.esc="closeCandidateModal"
      @keydown.left="previousCandidateImage"
      @keydown.right="nextCandidateImage"
    >
      <!-- 模态框头部信息 -->
      <template #header>
        <div class="modal-header">
          <span class="modal-title">截图预览</span>
          <div class="modal-info">
            <!-- 选择按钮移动到位置信息左侧 -->
            <div
              class="modal-select-button"
              :class="{ selected: isCandidateImageSelected() }"
              @click="toggleCandidateImageSelection"
            >
              <el-icon><Check v-if="isCandidateImageSelected()" /><Plus v-else /></el-icon>
            </div>
            <span class="image-position"
              >{{ screenshotState.candidateDisplayIndex + 1 }} /
              {{ sortedCandidateScreenshots.length }}</span
            >
            <span class="image-scale"
              >{{ Math.round(screenshotState.candidateImageScale * 100) }}%</span
            >
          </div>
        </div>
      </template>

      <div class="modal-content">
        <!-- 左侧导航按钮 -->
        <button
          class="nav-button nav-button-left"
          :disabled="screenshotState.candidateDisplayIndex === 0"
          @click="previousCandidateImage"
        >
          <el-icon><ArrowLeft /></el-icon>
        </button>

        <!-- 图片容器 -->
        <div
          class="modal-image-container"
          @click="closeCandidateModal"
          @wheel.prevent="handleCandidateImageWheel"
        >
          <img
            :src="screenshotState.candidateCurrentImage"
            alt="截图预览"
            class="modal-image"
            :style="{ transform: `scale(${screenshotState.candidateImageScale})` }"
            @click.stop
          />
        </div>

        <!-- 右侧导航按钮 -->
        <button
          class="nav-button nav-button-right"
          :disabled="
            screenshotState.candidateDisplayIndex === sortedCandidateScreenshots.length - 1
          "
          @click="nextCandidateImage"
        >
          <el-icon><ArrowRight /></el-icon>
        </button>
      </div>

      <!-- 底部控制栏 -->
      <template #footer>
        <div class="modal-footer">
          <div class="modal-controls-center">
            <!-- 缩放控制 -->
            <el-button-group size="small">
              <el-button
                @click="zoomCandidateImage('out')"
                :disabled="screenshotState.candidateImageScale <= 0.5"
              >
                <el-icon><ZoomOut /></el-icon>
              </el-button>
              <el-button @click="resetCandidateImageScale"> 重置 </el-button>
              <el-button
                @click="zoomCandidateImage('in')"
                :disabled="screenshotState.candidateImageScale >= 2"
              >
                <el-icon><ZoomIn /></el-icon>
              </el-button>
            </el-button-group>
          </div>

          <div class="modal-controls-right">
            <el-button size="small" @click="closeCandidateModal">关闭</el-button>
          </div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, onBeforeUnmount, computed, watch, nextTick, h } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useDebounceFn } from '@vueuse/core'
import { floatingWindowManager } from '@/services/FloatingWindowManager'
import { useAuthStore } from '@/stores/auth'
import { useUserStore } from '@/stores/user'
import { tokenManager } from '@/utils/tokenManager'
import { apiService, type CaseNode } from '@/services/api'
import { useCaseManagement } from '@/composables/useCaseManagement'
import { useBrowserConnection } from '@/composables/useBrowserConnection'

import { useRouter } from 'vue-router'
import {
  Plus,
  Minus,
  Delete,
  Edit,
  Search,
  Refresh,
  RefreshLeft,
  Check,
  Download,
  Upload,
  Close,
  ArrowLeft,
  ArrowRight,
  ZoomIn,
  ZoomOut,
  FullScreen,
  Picture,
  Connection,
  VideoPlay,
  VideoPause,
  VideoCamera,
  Folder,
  Document,
  Setting,
  Files,
  DocumentAdd,
  Link,
  Monitor,
  Top,
  Grid,
  Expand,
  Fold,
  Pointer,
  RefreshRight,
} from '@element-plus/icons-vue'

// ============================================================================
// API 服务配置
// ============================================================================
const API_BASE_URL = '' // 使用空字符串，通过Vite代理

// 初始化认证stores
const authStore = useAuthStore()
const userStore = useUserStore()







// 获取Access Token的辅助函数
const getAccessToken = (): string | null => {
  return tokenManager.getAccessToken()
}

// 获取Refresh Token的辅助函数
const getRefreshToken = (): string | null => {
  return tokenManager.getRefreshToken()
}

// 检查Access Token是否即将过期（兼容性函数）
const isAccessTokenExpiringSoon = (token: string): boolean => {
  return tokenManager.isTokenExpiringSoon(token)
}







// API响应接口
interface ApiResponse {
  status: string
  message?: string
  data?: any
}



// 智能token刷新函数（使用统一token管理器）
const smartRefreshToken = async (): Promise<{ success: boolean; token?: string; error?: string }> => {
  try {
    const result = await tokenManager.smartRefreshToken()

    if (result.success) {
      return {
        success: true,
        token: result.accessToken,
      }
    } else {
      return {
        success: false,
        error: result.message || 'Token刷新失败',
      }
    }
  } catch (error: any) {
    return {
      success: false,
      error: error.message || 'Token刷新异常',
    }
  }
}



// 通用API请求函数
const apiRequest = async (url: string, options: RequestInit = {}): Promise<ApiResponse> => {
  // 使用智能token刷新机制
  const tokenResult = await smartRefreshToken()

  if (!tokenResult.success) {
    if (tokenResult.error?.includes('Refresh token已过期')) {
      console.error('Refresh token已过期，需要重新登录')
      await handleAuthenticationFailure()
      throw new Error('登录已过期，请重新登录')
    } else if (tokenResult.error?.includes('刷新过于频繁')) {
      // 使用当前token继续请求，如果失败再处理
      const currentToken = getAccessToken()
      if (!currentToken) {
        throw new Error('未找到认证token，请重新登录')
      }
    } else {
      console.error('Token获取失败:', tokenResult.error)
      throw new Error('认证失败，请重新登录')
    }
  }

  const token = tokenResult.token || getAccessToken()
  if (!token) {
    throw new Error('未找到有效的认证token，请重新登录')
  }

  const defaultHeaders: Record<string, string> = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`,
  }

  const config: RequestInit = {
    ...options,
    headers: {
      ...defaultHeaders,
      ...options.headers,
    },
  }

  try {
    const response = await fetch(`${API_BASE_URL}${url}`, config)

    if (!response.ok) {
      if (response.status === 401) {
        console.error('API请求认证失败 (401)，尝试刷新token')

        // 使用统一token管理器刷新token并重试
        const retryResult = await smartRefreshToken()
        if (retryResult.success && retryResult.token) {
          // 使用新token重试请求
          const retryConfig: RequestInit = {
            ...options,
            headers: {
              ...defaultHeaders,
              'Authorization': `Bearer ${retryResult.token}`,
              ...options.headers,
            },
          }

          const retryResponse = await fetch(`${API_BASE_URL}${url}`, retryConfig)
          if (retryResponse.ok) {
            return await retryResponse.json()
          }
        }

        // 如果刷新失败或重试仍然401，则清理认证状态
        console.error('Token刷新失败或重试仍然失败，清理认证状态')
        await handleAuthenticationFailure()
        throw new Error('认证失败，请重新登录')
      }

      // 尝试解析错误响应
      let errorMessage = `HTTP ${response.status}: ${response.statusText}`
      try {
        const errorData = await response.json()
        if (errorData.message) {
          errorMessage = errorData.message
        }
      } catch {
        // 忽略JSON解析错误，使用默认错误消息
      }

      throw new Error(errorMessage)
    }

    return await response.json()
  } catch (error: any) {
    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      throw new Error('无法连接到服务器，请检查网络连接')
    }
    throw error
  }
}



// 处理认证失败的统一函数
const handleAuthenticationFailure = async (reason?: string): Promise<void> => {
  try {
    // 使用统一token管理器清理认证状态
    tokenManager.clearTokens()

    // 清理store状态
    await authStore.logout()
    await userStore.logout()

    // 显示提示信息
    const message = reason || '登录已过期，请重新登录'
    ElMessage.error(message)

    // 跳转到登录页面
    setTimeout(() => {
      router.push('/login')
    }, 1500)
  } catch (error) {
    console.error('处理认证失败时出错:', error)
    // 强制跳转到登录页面
    router.push('/login')
  }
}

// 浏览器激活参数接口
// 浏览器连接参数接口
interface BrowserConnectParams {
  remote_host: string
  remote_port: number
}

// 多用户系统浏览器连接响应接口
interface BrowserConnectResponse {
  status: string
  message: string
  data: {
    user_id: string
    username: string
    remote_host: string
    remote_port: number
    connection_state: string
    user_pages_count: number
    pool_stats: {
      total_connections: number
      active_connections: number
      total_users: number
      total_pages: number
    }
  }
  timestamp: string
  request_id: string | null
}

// 录制操作响应接口
interface RecordingResponse {
  success: boolean
  message: string
  user_id: string
  file_path?: string | null
}






// ============================================================================
// 用户认证状态管理
// ============================================================================
// 注意：现在使用真实的认证stores，不再需要模拟实现

// ============================================================================
// 类型定义
// ============================================================================
declare global {
  interface Window {
    getCurrentUserToken?: () => string | null
  }
}

// 测试步骤接口 - 与API保持一致
interface TestStep {
  stepNo: string
  inputAction: string
  expectedResult: string
  actualResult?: string
  evaluationCriteria?: string
  testConclusion?: string
}

// 用例详情接口
interface UsecaseDetail {
  testCaseName?: string
  identifier?: string
  traceRelation?: string
  testCaseSummary?: string
  usecaseInitialization?: string
  prerequisitesConstraints?: string
  testMethod?: string
  testResult?: string
  testSteps?: TestStep[]
  testCaseTerminationCondition?: string
  testCasePassCriteria?: string
  executionStatus?: string
  executionResult?: string
  issueIdentification?: string
  designer?: string
  reviewer?: string
  testTime?: string
  versions?: Version[]
}

// 测试数据接口
interface TestData {
  inputPerson: string
  testStep: string
  actualResult: string
  evaluationCriteria: string
  testResult: string
}

// 附件接口
interface Attachment {
  id: number | string
  name: string
  url: string
  type: string
  size?: number
  file?: File | null
}

// 版本接口
interface Version {
  id: number
  name: string
  data: any
  timestamp: number
  isInitial?: boolean
}

// 使用API中定义的CaseNode类型，并扩展需要的属性
interface TreeNode extends CaseNode {
  usecaseDetail?: UsecaseDetail
  testData?: TestData
  _stopWatcher?: () => void
  attachments?: Attachment[]
  label?: string
  parent?: TreeNode
  data?: any
  description?: string
}

// 移除重复的接口定义，使用上面已定义的接口

interface DragState {
  isDragging: boolean
  draggedNode: TreeNode | null
  dragOverNode: TreeNode | null
  canDrag: boolean
  dragStartPosition: { x: number; y: number } | null
  dropPosition: 'after' | 'before' | 'into'
  dragStartTime: number
  longPressTimer: number | null
}

interface Screenshot {
  id: number
  url: string
  thumbnail: string
  title: string
  timestamp: string
}



interface ProductDragState {
  isDragging: boolean
  draggedNode: TreeNode | null
  dragOverNode: TreeNode | null
  canDrag: boolean
  dropPosition: 'after' | 'before' | 'into'
}

// ElTreeNode 接口已在上面定义，移除重复定义

// ============================================================================
// 响应式数据定义
// ============================================================================

// 选择状态管理
const selectedProduct = ref<TreeNode | null>(null)
// 删除未使用的selectedCandidate常量
const selectedTestCase = ref<TreeNode | null>(null)
const selectedCandidateTestCase = ref<TreeNode | null>(null)

// 浏览器连接状态
const browserConnected = ref(false)
const connectingBrowser = ref(false)

// 录制悬浮窗状态
const floatingWindowOpen = ref(false)

// 附件管理
const attachmentDialogVisible = ref(false)
const newAttachment = reactive({
  name: '',
  file: null as File | null,
})
// 文件列表项接口
interface FileListItem {
  name: string
  size: number
  type: string
  raw: File
}

const fileList = ref<FileListItem[]>([])

// 录制状态管理
const recordingState = reactive({
  isRecording: false,
  isPaused: false,
  currentUsecaseName: '',
  startTime: null as Date | null,
  duration: 0,
})

// 截图展示相关状态
const screenshotState = reactive({
  // 产品库截图状态
  productSelectedIndices: [] as number[], // 存储原始索引
  productSelectionOrder: new Map<number, number>(), // 存储选择顺序：原始索引 -> 选择顺序
  productModalVisible: false,
  productCurrentImage: '',
  productCurrentIndex: 0, // 当前显示的图片在原始数组中的索引
  productDisplayIndex: 0, // 当前显示的图片在排序列表中的索引
  productImageScale: 1, // 图片缩放比例
  productHoveredIndex: -1,
  productDraggedIndex: -1,
  productDragOverIndex: -1,

  // 备选库截图状态
  candidateSelectedIndices: [] as number[], // 存储原始索引
  candidateSelectionOrder: new Map<number, number>(), // 存储选择顺序：原始索引 -> 选择顺序
  candidateModalVisible: false,
  candidateCurrentImage: '',
  candidateCurrentIndex: 0, // 当前显示的图片在原始数组中的索引
  candidateDisplayIndex: 0, // 当前显示的图片在排序列表中的索引
  candidateImageScale: 1, // 图片缩放比例
  candidateHoveredIndex: -1,
  candidateDraggedIndex: -1,
  candidateDragOverIndex: -1,
})

// 截图数据 - 从录制系统获取
const screenshots = ref<Screenshot[]>([])

// UI状态管理
const productLibraryCollapsed = ref(false)
const candidateLibraryCollapsed = ref(false)
const hoveredNodeId = ref<number | string | null>(null)
const candidateHoveredNodeId = ref<number | string | null>(null)

// 用例详情表单数据 - 与API接口保持一致
const usecaseDetailForm = reactive({
  caseName: '',
  identifier: '', // 用例标识
  traceability: '',
  summary: '',
  initialization: '',
  prerequisites: '',
  testMethod: '',
  terminationCriteria: '',
  acceptanceCriteria: '',
  executionStatus: '',
  executionResult: '',
  designer: '',
  reviewer: '',
  testTime: '',
  issueId: '',
  testSteps: [] as TestStep[],
  versions: [] as any[]  // 更新为版本信息对象数组
})

// 用例详情相关状态
const currentCaseDetail = ref<any>(null)
const selectedCaseId = ref<string>('')
const selectedTableId = ref<string>('')
const isLoadingCaseDetail = ref(false)
const isSavingCase = ref(false)

// 测试时间日期选择器相关状态
const productTestDate = ref<string>('')
const candidateTestDate = ref<string>('')

// 监听产品库测试时间字段变化，同步到日期选择器
watch(() => usecaseDetailForm.testTime, (newValue) => {
  if (newValue) {
    const date = parseDateString(newValue)
    if (date) {
      productTestDate.value = date.toISOString().split('T')[0]
    }
  } else {
    productTestDate.value = ''
  }
})

const selectedVersionIndex = ref(0) // 当前选择的版本索引
const isFormModified = ref(false) // 产品库表单是否被修改
const originalFormData = ref<any>(null) // 产品库原始表单数据，用于比较
const isCandidateFormModified = ref(false) // 备选库表单是否被修改
const originalCandidateFormData = ref<any>(null) // 备选库原始表单数据，用于比较

// 监听产品库表单修改
watch(
  () => [
    usecaseDetailForm.caseName,
    usecaseDetailForm.traceability,
    usecaseDetailForm.summary,
    usecaseDetailForm.initialization,
    usecaseDetailForm.prerequisites,
    usecaseDetailForm.testMethod,
    usecaseDetailForm.terminationCriteria,
    usecaseDetailForm.acceptanceCriteria,
    usecaseDetailForm.executionStatus,
    usecaseDetailForm.executionResult,
    usecaseDetailForm.designer,
    usecaseDetailForm.reviewer,
    usecaseDetailForm.testTime,
    usecaseDetailForm.testSteps
  ],
  () => {
    isFormModified.value = checkFormModified()
  },
  { deep: true }
)

// 备选库表单修改监听将在candidateDetailForm声明后添加

// 浏览器关闭前的提醒
const handleBeforeUnload = (event: BeforeUnloadEvent) => {
  if (isFormModified.value) {
    const message = '产品库用例有未保存的修改，关闭页面将丢失这些修改。'
    event.preventDefault()
    event.returnValue = message
    return message
  }

  if (isCandidateFormModified.value) {
    const message = '备选库用例有未保存的修改，请将用例拖动到产品库并保存数据。'
    event.preventDefault()
    event.returnValue = message
    return message
  }
}

// 页面离开前的提醒
onBeforeUnmount(() => {
  if (isFormModified.value || isCandidateFormModified.value) {
    console.warn('页面即将离开，但有未保存的修改')
  }
  // 移除浏览器关闭前的监听器
  window.removeEventListener('beforeunload', handleBeforeUnload)
})

// 添加浏览器关闭前的监听器
onMounted(() => {
  window.addEventListener('beforeunload', handleBeforeUnload)
})

// 监听路由变化，在切换路由前提醒用户保存
const router = useRouter()
router.beforeEach(async (to: any, from: any, next: any) => {
  if (from.path === '/management/usecase') {
    // 检查产品库表单修改
    if (isFormModified.value) {
      try {
        await ElMessageBox.confirm(
          '产品库用例有未保存的修改，离开页面将丢失这些修改。是否继续？',
          '提示',
          {
            confirmButtonText: '离开页面',
            cancelButtonText: '取消',
            type: 'warning',
          }
        )
      } catch {
        next(false)
        return
      }
    }

    // 检查备选库表单修改
    if (isCandidateFormModified.value) {
      try {
        await ElMessageBox.confirm(
          '备选库用例有未保存的修改，请将用例拖动到产品库并保存数据。是否继续离开？',
          '提示',
          {
            confirmButtonText: '离开页面',
            cancelButtonText: '取消',
            type: 'warning',
          }
        )
      } catch {
        next(false)
        return
      }
    }
  }

  next()
})

// 导出相关数据
const exportDialogVisible = ref(false)
const selectedExportSystems = ref<(string | number)[]>([])

// 搜索相关
const searchTarget = ref('tree')
const searchKeyword = ref('')
const candidateSearchTarget = ref('tree')
const candidateSearchKeyword = ref('')
// Element Plus Tree 组件引用类型
interface ElTreeInstance {
  filter: (value: string) => void
  setCurrentKey: (key: string | number) => void
  getNode: (key: string | number) => any
}

const productTreeRef = ref<ElTreeInstance | null>(null)
const candidateTreeRef = ref<ElTreeInstance | null>(null)

// 旧的版本管理相关变量已删除，现在使用新的版本管理系统

// 旧的数据变更检测变量已删除，现在使用isFormModified

// 旧的版本记录变量已删除

// ============================================================================
// 工具函数
// ============================================================================

// 获取所有测试用例的扁平化列表
const getAllTestCases = (tree: TreeNode[]): TreeNode[] => {
  const testCases: TreeNode[] = []

  const traverse = (nodes: TreeNode[]) => {
    for (const node of nodes) {
      if (node.type === 'testCase') {
        testCases.push(node)
      }
      if (node.children && node.children.length > 0) {
        traverse(node.children)
      }
    }
  }

  traverse(tree)
  return testCases
}

// 获取添加按钮的提示文本
const getAddButtonTitle = (nodeType: string): string => {
  const titleMap = {
    system: '添加功能',
    function: '添加测试项',
    testItem: '添加用例',
    testCase: '添加用例',
  }
  return titleMap[nodeType as keyof typeof titleMap] || '添加'
}

// 获取节点类型名称
const getNodeTypeName = (nodeType: string): string => {
  const nameMap = {
    system: '系统',
    function: '功能',
    testItem: '测试项',
    testCase: '测试用例',
  }
  return nameMap[nodeType as keyof typeof nameMap] || '节点'
}

// 获取节点类型的基础名称
const getBaseName = (nodeType: string): string => {
  const baseNameMap = {
    system: '系统',
    function: '功能名称',
    testItem: '测试项',
    testCase: '用例',
  }
  return baseNameMap[nodeType as keyof typeof baseNameMap] || '节点'
}

// 获取节点的原始名称（去除序号后缀）
const getOriginalNodeName = (name: string): string => {
  // 如果名称以 -数字 结尾，则去除序号后缀
  const match = name.match(/^(.+)-\d+$/)
  return match ? match[1] : name
}

// ============================================================================
// 计算属性
// ============================================================================

// 用例导航计算属性
const canGoPreviousTestCase = computed(() => {
  if (!selectedTestCase.value) return false
  const allTestCases = getAllTestCases(productLibrary.value || [])
  const currentIndex = allTestCases.findIndex((tc) => tc.id === selectedTestCase.value?.id)
  return currentIndex > 0
})

const canGoNextTestCase = computed(() => {
  if (!selectedTestCase.value) return false
  const allTestCases = getAllTestCases(productLibrary.value || [])
  const currentIndex = allTestCases.findIndex((tc) => tc.id === selectedTestCase.value?.id)
  return currentIndex < allTestCases.length - 1
})

const canGoPreviousCandidateTestCase = computed(() => {
  if (!selectedCandidateTestCase.value) return false
  const allTestCases = getAllTestCases(candidateLibrary.value || [])
  const currentIndex = allTestCases.findIndex((tc) => tc.id === selectedCandidateTestCase.value?.id)
  return currentIndex > 0
})

const canGoNextCandidateTestCase = computed(() => {
  if (!selectedCandidateTestCase.value) return false
  const allTestCases = getAllTestCases(candidateLibrary.value || [])
  const currentIndex = allTestCases.findIndex((tc) => tc.id === selectedCandidateTestCase.value?.id)
  return currentIndex < allTestCases.length - 1
})

// 旧的保存按钮状态计算已删除

// 步骤删除状态计算
const canRemoveStep = computed(() => {
  return usecaseDetailForm.testSteps.length > 0
})

const candidateCanRemoveStep = computed(() => {
  return candidateSelectedRowIndex.value !== null && candidateDetailForm.testSteps.length > 1
})

// 旧的版本管理计算属性已删除

// 系统选项接口
interface SystemOption {
  id: string | number
  name: string
}

// 系统列表计算属性
const availableSystems = computed((): SystemOption[] => {
  return (productLibrary.value || []).map((system: any) => ({
    id: system.id,
    name: system.name,
  }))
})

// 截图排序计算属性
const sortedProductScreenshots = computed(() => {
  const screenshotList = [...screenshots.value]
  const selected = screenshotList
    .map((item, index) => ({ item, originalIndex: index }))
    .filter(({ originalIndex }) => screenshotState.productSelectedIndices.includes(originalIndex))
    .sort((a, b) => {
      const orderA = screenshotState.productSelectionOrder.get(a.originalIndex) || 0
      const orderB = screenshotState.productSelectionOrder.get(b.originalIndex) || 0
      return orderA - orderB
    })
    .map(({ item }) => item)

  const unselected = screenshotList.filter(
    (_, index) => !screenshotState.productSelectedIndices.includes(index),
  )
  return [...selected, ...unselected]
})

const sortedCandidateScreenshots = computed(() => {
  const screenshotList = [...screenshots.value]
  const selected = screenshotList
    .map((item, index) => ({ item, originalIndex: index }))
    .filter(({ originalIndex }) => screenshotState.candidateSelectedIndices.includes(originalIndex))
    .sort((a, b) => {
      const orderA = screenshotState.candidateSelectionOrder.get(a.originalIndex) || 0
      const orderB = screenshotState.candidateSelectionOrder.get(b.originalIndex) || 0
      return orderA - orderB
    })
    .map(({ item }) => item)

  const unselected = screenshotList.filter(
    (_, index) => !screenshotState.candidateSelectedIndices.includes(index),
  )
  return [...selected, ...unselected]
})

// 树形过滤计算属性 - 使用动态数据
const filteredProductTree = computed(() => {
  // 使用从 useCaseManagement 获取的动态数据
  const dynamicProductTree = productLibrary?.value || []

  if (!searchKeyword.value || searchTarget.value !== 'tree') {
    return dynamicProductTree
  }

  const filterTree = (nodes: any[]): any[] => {
    return nodes.filter((node) => {
      const matchesKeyword = node.name?.toLowerCase().includes(searchKeyword.value.toLowerCase()) ?? false
      const hasMatchingChildren =
        node.children && node.children.length > 0 ? filterTree(node.children).length > 0 : false

      if (matchesKeyword || hasMatchingChildren) {
        if (node.children && node.children.length > 0) {
          node.children = filterTree(node.children)
        }
        return true
      }
      return false
    })
  }

  return filterTree([...dynamicProductTree])
})

const filteredCandidateTree = computed(() => {
  // 使用从 useCaseManagement 获取的动态数据
  const dynamicCandidateTree = candidateLibrary?.value || []

  if (!candidateSearchKeyword.value || candidateSearchTarget.value !== 'tree') {
    return dynamicCandidateTree
  }

  const filterTree = (nodes: any[]): any[] => {
    return nodes.filter((node) => {
      const matchesKeyword = node.name
        ?.toLowerCase()
        .includes(candidateSearchKeyword.value.toLowerCase()) ?? false
      const hasMatchingChildren =
        node.children && node.children.length > 0 ? filterTree(node.children).length > 0 : false

      if (matchesKeyword || hasMatchingChildren) {
        if (node.children && node.children.length > 0) {
          node.children = filterTree(node.children)
        }
        return true
      }
      return false
    })
  }

  return filterTree([...dynamicCandidateTree])
})

// 树形数据配置
const treeProps = {
  children: 'children',
  label: 'name',
}

// 产品库数据现在使用动态API数据，不再需要硬编码


// 表格选择状态
const selectedRowIndex = ref<number | null>(null)
const candidateSelectedRowIndex = ref<number | null>(null)

// 拖拽相关状态
const dragState = reactive<DragState>({
  isDragging: false,
  draggedNode: null,
  dragStartTime: 0,
  longPressTimer: null,
  dragOverNode: null,
  canDrag: false,
  dragStartPosition: null,
  dropPosition: 'after',
})

// 拖拽配置
const LONG_PRESS_DURATION = 500 // 长按时间（毫秒）

// 产品库内部拖拽状态
const productDragState = reactive<ProductDragState>({
  isDragging: false,
  draggedNode: null,
  dragOverNode: null,
  canDrag: false,
  dropPosition: 'after', // 'after', 'before', 'into'
})

// 备选库数据现在使用动态API数据，不再需要硬编码




// 备选库选择状态
const selectedCandidateNode = ref<TreeNode | null>(null)

// 备选库表单数据
const candidateDetailForm = reactive({
  testCaseName: '',
  identifier: '',
  traceRelation: '',
  testCaseSummary: '',
  usecaseInitialization: '',
  prerequisitesConstraints: '',
  testMethod: '',
  testResult: '', // 新增测试结果字段
  testSteps: [
    {
      id: 1,
      sequence: 1,
      inputOperation: '',
      expectedResult: '',
      actualResult: '',
      evaluationCriteria: '',
      testConclusion: '',
    },
  ],
  testCaseTerminationCondition: '',
  testCasePassCriteria: '',
  executionStatus: '',
  executionResult: '',
  issueIdentification: '',
  designer: '',
  reviewer: '',
  testTime: '',
})

// 监听备选库测试时间字段变化，同步到日期选择器
watch(() => candidateDetailForm.testTime, (newValue) => {
  if (newValue) {
    const date = parseDateString(newValue)
    if (date) {
      candidateTestDate.value = date.toISOString().split('T')[0]
    }
  } else {
    candidateTestDate.value = ''
  }
})

// 监听备选库表单修改
watch(
  () => [
    candidateDetailForm.testCaseName,
    candidateDetailForm.identifier,
    candidateDetailForm.traceRelation,
    candidateDetailForm.testCaseSummary,
    candidateDetailForm.usecaseInitialization,
    candidateDetailForm.prerequisitesConstraints,
    candidateDetailForm.testMethod,
    candidateDetailForm.testCaseTerminationCondition,
    candidateDetailForm.testCasePassCriteria,
    candidateDetailForm.executionStatus,
    candidateDetailForm.executionResult,
    candidateDetailForm.designer,
    candidateDetailForm.reviewer,
    candidateDetailForm.testTime,
    candidateDetailForm.testSteps
  ],
  () => {
    isCandidateFormModified.value = checkCandidateFormModified()
  },
  { deep: true }
)

// 高度自适应相关
const productLibraryRef = ref<HTMLElement | null>(null)
const candidateLibraryRef = ref<HTMLElement | null>(null)
const usecaseDetailRef = ref<HTMLElement | null>(null)
const candidateDetailRef = ref<HTMLElement | null>(null)
const resizeObserver = ref<ResizeObserver | null>(null)

// 删除未使用的filteredTableData计算属性

// ============================================================================
// UI 布局与交互函数
// ============================================================================

// 高度自适应方法
const updateLibraryHeights = () => {
  nextTick(() => {
    try {
      // 获取用例详情区域的高度
      const usecaseDetailHeight = usecaseDetailRef.value?.offsetHeight || 0
      const candidateDetailHeight = candidateDetailRef.value?.offsetHeight || 0

      // 取两者中的较大值作为目标高度
      const targetHeight = Math.max(usecaseDetailHeight, candidateDetailHeight, 600) // 最小600px

      // 设置产品库和备选库的高度
      if (productLibraryRef.value) {
        productLibraryRef.value.style.minHeight = `${targetHeight}px`
      }
      if (candidateLibraryRef.value) {
        candidateLibraryRef.value.style.minHeight = `${targetHeight}px`
      }
    } catch (error) {
      // 高度自适应更新失败时的处理
    }
  })
}

// ============================================================================
// 监听器
// ============================================================================

// 统一的高度更新监听器
const setupHeightWatchers = () => {
  // 监听测试步骤数据变化
  watch(
    () => usecaseDetailForm.testSteps.length,
    () => {
      setTimeout(updateLibraryHeights, 50)
    },
    { flush: 'post' },
  )

  watch(
    () => candidateDetailForm.testSteps.length,
    () => {
      setTimeout(updateLibraryHeights, 50)
    },
    { flush: 'post' },
  )

  // 监听选中的测试用例变化
  watch(
    [selectedTestCase, selectedCandidateTestCase],
    () => {
      setTimeout(updateLibraryHeights, 100)
    },
    { flush: 'post' },
  )

  // 监听表单数据的深度变化
  watch(
    [() => usecaseDetailForm, () => candidateDetailForm],
    () => {
      setTimeout(updateLibraryHeights, 200)
    },
    { deep: true, flush: 'post' },
  )
}

// ============================================================================
// 产品库管理函数
// ============================================================================

const handleProductNodeClick = async (data: TreeNode) => {
  console.log('🖱️ 产品库节点点击:', {
    name: data.name,
    type: data.type,
    id: data.id,
    tableName: data.tableName,
    originalName: data.originalName,
    systemKey: data.systemKey,
    fullData: data
  })

  // 检查产品库是否有未保存的修改
  if (isFormModified.value) {
    try {
      await ElMessageBox.confirm(
        '产品库用例有未保存的修改，切换用例将丢失这些修改。是否继续？',
        '提示',
        {
          confirmButtonText: '继续切换',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )
    } catch {
      // 用户取消，不进行切换
      return
    }
  }

  // 检查备选库是否有未保存的修改
  if (isCandidateFormModified.value) {
    try {
      await ElMessageBox.confirm(
        '备选库用例有未保存的修改，请将用例拖动到产品库并保存数据。是否继续切换？',
        '提示',
        {
          confirmButtonText: '继续切换',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )
    } catch {
      // 用户取消，不进行切换
      return
    }
  }

  if (data.type === 'testCase') {
    console.log('📋 这是一个测试用例，开始加载数据...')
    selectedProduct.value = data
    selectedTestCase.value = data

    // 旧的版本初始化调用已删除

    // 加载用例详情数据
    loadTestCaseData(data)

    // 加载截图选中状态
    loadProductScreenshotSelection()

    // 清除选中状态
    selectedRowIndex.value = null
  } else {
    // 如果不是测试用例，清空选择
    selectedTestCase.value = null
    selectedProduct.value = data
    // 清除版本相关状态
    isFormModified.value = false
    originalFormData.value = null
    // 清空截图选中状态
    screenshotState.productSelectedIndices = []
    screenshotState.productSelectionOrder.clear()
  }
}

// ============================================================================
// 版本管理函数
// ============================================================================

// 旧的版本初始化函数已删除，现在使用新的版本管理系统

// 加载测试用例数据
const loadTestCaseData = async (testCase: TreeNode) => {
  console.log('🔍 loadTestCaseData 被调用:', {
    testCaseName: testCase.name,
    testCaseId: testCase.id,
    tableName: testCase.tableName,
    hasTableName: !!testCase.tableName,
    hasId: !!testCase.id
  })

  // 获取tableName - 优先使用节点自身的tableName，如果没有则从父节点获取
  let tableName = testCase.tableName
  if (!tableName) {
    // 从父节点获取tableName
    const parentNode = findParentNode(productLibrary.value || [], testCase.id)
    if (parentNode && parentNode.tableName) {
      tableName = parentNode.tableName
      console.log('🔍 从父节点获取tableName:', tableName)
    }
  }

  // 尝试从API获取用例详情
  if (tableName && testCase.id) {
    try {
      console.log('📡 发起API请求获取用例详情:', {
        tableName: tableName,
        id: testCase.id.toString()
      })
      await fetchCaseDetail(tableName, testCase.id.toString())
      return // 如果API调用成功，直接返回
    } catch (error) {
      console.warn('从API获取用例详情失败，使用本地数据:', error)
      // 继续使用本地数据作为后备方案
    }
  } else {
    console.warn('⚠️ 无法发起API请求，缺少必要参数:', {
      tableName: tableName,
      id: testCase.id,
      missingTableName: !tableName,
      missingId: !testCase.id
    })
  }

  // 后备方案：使用本地数据
  console.log('使用本地数据作为后备方案')

  // 创建默认数据
  Object.assign(usecaseDetailForm, createDefaultUsecaseDetail(testCase))

  // 保存原始数据用于变更检测
  saveOriginalFormData()

  // 开始监听表单变更
  startFormChangeDetection()
}

// 创建默认用例详情数据
const createDefaultUsecaseDetail = (testCase: TreeNode): UsecaseDetail => {
  return {
    testCaseName: testCase.name || '',
    identifier: generateProductIdentifier(testCase),
    traceRelation: '',
    testCaseSummary: '',
    usecaseInitialization: '',
    prerequisitesConstraints: '',
    testMethod: '',
    testSteps: [
      {
        stepNo: '1',
        inputAction: '',
        expectedResult: '',
        actualResult: '',
        evaluationCriteria: '',
        testConclusion: '',
      },
    ],
    testCaseTerminationCondition: '',
    testCasePassCriteria: '',
    executionStatus: '',
    executionResult: '',
    issueIdentification: '',
    designer: '',
    reviewer: '',
    testTime: '',
  }
}

// 开始表单变更检测
const startFormChangeDetection = () => {
  // 使用防抖的深度监听来优化性能
  const debouncedFormWatcher = useDebounceFn((newVal: any, oldVal: any) => {
    if (originalFormData.value) {
      const currentData = JSON.stringify(newVal)
      const originalData = JSON.stringify(originalFormData.value)
      isFormModified.value = currentData !== originalData

      // 检查测试用例名称是否发生变化，如果是则同步更新树形目录
      if (oldVal && newVal.testCaseName !== oldVal.testCaseName && selectedTestCase.value) {
        updateTreeNodeName(selectedTestCase.value, newVal.testCaseName)
      }
    }
  }, 300) // 300ms防抖延迟

  const stopWatcher = watch(() => usecaseDetailForm, debouncedFormWatcher, { deep: true })

  // 当切换用例时停止之前的监听
  if (selectedTestCase.value) {
    // 存储停止函数，以便在切换用例时调用
    selectedTestCase.value._stopWatcher = stopWatcher
  }
}

// 树形目录收起/展开方法
const toggleTreeCollapse = () => {
  productLibraryCollapsed.value = !productLibraryCollapsed.value
}

// 初始化用例详情表单
const initializeUsecaseDetailForm = () => {
  Object.assign(usecaseDetailForm, {
    testCaseName: '',
    identifier: '',
    traceRelation: '',
    testCaseSummary: '',
    usecaseInitialization: '',
    prerequisitesConstraints: '',
    testMethod: '',
    testResult: '',
    testSteps: [],
    testCaseTerminationCondition: '',
    testCasePassCriteria: '',
    executionStatus: '',
    executionResult: '',
    issueIdentification: '',
    designer: '',
    reviewer: '',
    testTime: '',
  })
}

// 注意：测试步骤操作方法已移至用例详情相关方法部分

// 单选框点击处理方法
const handleRadioClick = (index: number) => {
  // 如果点击的是已选中的单选框，则取消选中
  if (selectedRowIndex.value === index) {
    selectedRowIndex.value = null
  }
  // 如果点击的是其他单选框，el-radio会自动处理选中状态
}

// Element Plus Tree 节点接口
interface ElTreeNode {
  data: TreeNode
  parent?: ElTreeNode
  level: number
  expanded: boolean
  checked: boolean
  indeterminate: boolean
  disabled: boolean
}

// 添加树形节点
const addTreeNode = (data: TreeNode, node: ElTreeNode): void => {
  const newId = generateNewId()
  let newNode: TreeNode = {
    id: newId,
    name: '新建节点',
    displayName: '新建节点',
    type: 'testCase',
  }

  switch (data.type) {
    case 'system':
      // 在系统下添加功能
      newNode = {
        ...newNode,
        name: '新建功能',
        type: 'function',
        children: [],
      }
      break
    case 'function':
      // 在功能下添加测试项
      newNode = {
        ...newNode,
        name: '新建测试项',
        displayName: '新建测试项',
        type: 'testItem',
        children: [],
      }
      break
    case 'testItem':
      // 在测试项下添加用例
      newNode = {
        id: `product-testcase-${newId}`,
        name: '新建用例',
        displayName: '新建用例',
        type: 'testCase',
        description: '新建测试用例',
        usecaseDetail: createDefaultUsecaseDetail({
          id: `product-testcase-${newId}`,
          name: '新建用例',
          type: 'testCase'
        } as TreeNode),
        testData: {
          inputPerson: '',
          testStep: '',
          actualResult: '',
          evaluationCriteria: '',
          testResult: '',
        },
      }

      // 如果有选中的测试用例，插入到其下方
      if (selectedTestCase.value && selectedTestCase.value.type === 'testCase') {
        const selectedParent = findParentNode(productLibrary.value || [], selectedTestCase.value.id)
        if (selectedParent && selectedParent.id === data.id) {
          // 选中的用例在当前测试项下，插入到选中用例的下方
          const selectedIndex =
            data.children?.findIndex(
              (child: TreeNode) => child.id === selectedTestCase.value?.id,
            ) ?? -1
          if (selectedIndex !== -1 && data.children) {
            data.children.splice(selectedIndex + 1, 0, newNode)
            // 重新编号所有测试用例
            reorderSiblings(data.children, 'testCase')

            // 生成正确的标识符
            if (newNode.usecaseDetail) {
              newNode.usecaseDetail.identifier = generateProductIdentifier(newNode)
            }

            ElMessage.success(`已在 ${selectedTestCase.value.name} 下方添加 ${newNode.name}`)

            // 立即进入编辑模式
            nextTick(() => {
              startEditNodeName(newNode, newNode.name, data)
            })
            return
          }
        }
      }
      break
    case 'testCase':
      // 在用例同级添加用例
      const parent = findParentNode(productLibrary.value || [], data.id)
      if (parent) {
        newNode = {
          id: `product-testcase-${newId}`,
          name: '新建用例',
          displayName: '新建用例',
          type: 'testCase',
          description: '新建测试用例',
          usecaseDetail: createDefaultUsecaseDetail({
            id: `product-testcase-${newId}`,
            name: '新建用例',
            type: 'testCase'
          } as TreeNode),
          testData: {
            inputPerson: '',
            testStep: '',
            actualResult: '',
            evaluationCriteria: '',
            testResult: '',
          },
        }

        // 找到当前用例在父节点children中的位置，插入到其下方
        const currentIndex =
          parent.children?.findIndex((child: TreeNode) => child.id === data.id) ?? -1
        if (currentIndex !== -1 && parent.children) {
          parent.children.splice(currentIndex + 1, 0, newNode)
          // 重新编号所有测试用例
          reorderSiblings(parent.children, 'testCase')

          // 生成正确的标识符
          if (newNode.usecaseDetail) {
            newNode.usecaseDetail.identifier = generateProductIdentifier(newNode)
          }

          ElMessage.success(`已在 ${data.name} 下方添加 ${newNode.name}`)

          // 立即进入编辑模式
          nextTick(() => {
            startEditNodeName(newNode, newNode.name, parent)
          })
        } else {
          // 如果找不到位置，则添加到末尾
          if (!parent.children) {
            parent.children = []
          }
          parent.children.push(newNode)
          // 重新编号所有测试用例
          reorderSiblings(parent.children, 'testCase')

          // 生成正确的标识符
          if (newNode.usecaseDetail) {
            newNode.usecaseDetail.identifier = generateProductIdentifier(newNode)
          }

          ElMessage.success(`添加 ${newNode.name} 成功`)

          // 立即进入编辑模式
          nextTick(() => {
            startEditNodeName(newNode, newNode.name, parent)
          })
        }
        return
      }
      break
  }

  if (!data.children) {
    data.children = []
  }
  data.children.push(newNode)

  // 如果是测试用例，重新编号并生成标识符
  if (newNode.type === 'testCase') {
    reorderSiblings(data.children, 'testCase')
    // 生成正确的标识符
    if (newNode.usecaseDetail) {
      newNode.usecaseDetail.identifier = generateProductIdentifier(newNode)
    }
  }

  // 立即进入编辑模式，传递父节点信息
  nextTick(() => {
    startEditNodeName(newNode, newNode.name, data)
  })
}

// 删除树形节点
const deleteTreeNode = (data: TreeNode, node: ElTreeNode): void => {
  ElMessageBox.confirm(`确定要删除 "${data.name}" 吗？此操作将同时删除其所有子节点。`, '确认删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      const parent = findParentNode(productLibrary.value || [], data.id)
      if (parent && parent.children) {
        const index = parent.children.findIndex((child: TreeNode) => child.id === data.id)
        if (index > -1) {
          parent.children.splice(index, 1)
          // 删除后重新排序同级节点
          reorderSiblings(parent.children, data.type)

          // 如果删除的是当前选中的节点，清除选择
          if (selectedTestCase.value?.id === data.id) {
            selectedTestCase.value = null
            selectedProduct.value = null
          }

          ElMessage.success(`已删除 ${data.name}`)
        }
      } else {
        // 删除根节点
        const productData = productLibrary.value || []
        const index = productData.findIndex((item: any) => item.id === data.id)
        if (index > -1) {
          productData.splice(index, 1)
          // 删除后重新排序根级节点
          reorderSiblings(productData, data.type)
          ElMessage.success(`已删除 ${data.name}`)
        }
      }
    })
    .catch(() => {
      // 用户取消删除
    })
}

// 删除用例节点（软删除）
const deleteCaseNode = async (data: TreeNode): Promise<void> => {
  try {
    await ElMessageBox.confirm(`确定要删除用例 "${data.name}" 吗？`, '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    // 调用删除用例API
    const caseIds = [data.id.toString()]
    await apiService.caseManagement.deleteCase({ case_ids: caseIds })

    // 标记为已删除
    data.deleted = true

    ElMessage.success(`用例 "${data.name}" 删除成功`)

    // 刷新数据
    await refreshSystems()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('删除用例失败：' + (error.message || '未知错误'))
    }
  }
}

// 恢复用例节点
const restoreCaseNode = async (data: TreeNode): Promise<void> => {
  try {
    await ElMessageBox.confirm(`确定要恢复用例 "${data.name}" 吗？`, '确认恢复', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info',
    })

    // 调用恢复用例API
    const caseIds = [data.id.toString()]
    await apiService.caseManagement.restoreCase({ case_ids: caseIds })

    // 标记为未删除
    data.deleted = false

    ElMessage.success(`用例 "${data.name}" 恢复成功`)

    // 刷新数据
    await refreshSystems()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('恢复用例失败：' + (error.message || '未知错误'))
    }
  }
}

// 查找父节点
const findParentNode = (nodes: TreeNode[], targetId: number | string): TreeNode | null => {
  for (const node of nodes) {
    if (node.children) {
      for (const child of node.children) {
        if (child.id === targetId) {
          return node
        }
      }
      const found = findParentNode(node.children, targetId)
      if (found) {
        return found
      }
    }
  }
  return null
}

// 生成新的ID
const generateNewId = (): string => {
  return (Date.now() + Math.random()).toString()
}

// 获取下一个序号
const getNextSequence = (siblings: TreeNode[] | undefined, type: string): number => {
  if (!siblings || siblings.length === 0) {
    return 1
  }

  const sameTypeNodes = siblings.filter((node: TreeNode) => node.type === type)
  const numbers = sameTypeNodes.map((node: TreeNode) => {
    const match = node.name.match(/\d+$/)
    return match ? parseInt(match[0]) : 0
  })

  return Math.max(...numbers, 0) + 1
}

// 解析用例名称，分离自定义名称和序号
const parseTestCaseName = (
  name: string,
): { customName: string; sequence: number; hasCustomName: boolean } => {
  // 匹配格式：用例-数字 或 自定义名称-数字
  const match = name.match(/^(.+)-(\d+)$/)
  if (match) {
    const [, customName, sequence] = match
    return {
      customName: customName,
      sequence: parseInt(sequence),
      hasCustomName: customName !== '用例',
    }
  }

  // 如果没有匹配到标准格式，返回原名称作为自定义名称
  return {
    customName: name,
    sequence: 1,
    hasCustomName: true,
  }
}

// 重构用例名称，保持自定义名称，更新序号
const rebuildTestCaseName = (originalName: string, newSequence: number) => {
  const parsed = parseTestCaseName(originalName)

  // 如果有自定义名称，保持自定义名称
  if (parsed.hasCustomName) {
    return `${parsed.customName}-${newSequence}`
  } else {
    // 如果是默认名称，使用标准格式
    return `用例-${newSequence}`
  }
}

// 重新排序同级节点（简化版）
const reorderSiblings = (siblings: TreeNode[], nodeType: string) => {
  if (!siblings || siblings.length === 0) {
    return
  }

  // 获取同类型的节点
  const sameTypeNodes = siblings.filter((node) => node.type === nodeType)

  // 只对测试用例重新编号，其他节点类型保持原始名称
  if (nodeType === 'testCase') {
    sameTypeNodes.forEach((node, index) => {
      const sequenceNumber = index + 1
      // 测试用例：保持自定义名称，只更新序号
      const parsed = parseTestCaseName(node.name)

      if (parsed.hasCustomName) {
        node.name = `${parsed.customName}-${sequenceNumber}`
      } else {
        node.name = `用例-${sequenceNumber}`
      }
    })
  }
  // 配置项和测试项不需要重新编号，保持原始名称
}

const importProduct = () => {
  ElMessage.info('导入产品功能')
}

// 显示导出对话框
const showExportPopover = () => {
  exportDialogVisible.value = true
}

// 确认导出
const confirmExport = () => {
  if (selectedExportSystems.value.length === 0) {
    ElMessage.warning('请至少选择一个系统进行导出')
    return
  }

  // 收集选中系统下的所有测试用例及其最后查看的版本数据
  const exportData = collectExportData(selectedExportSystems.value)

  const selectedSystemNames = selectedExportSystems.value
    .map((systemId: string | number) => {
      const system = availableSystems.value.find((s) => s.id === systemId)
      return system ? system.name : ''
    })
    .filter((name) => name)

  ElMessage.success(`正在导出系统：${selectedSystemNames.join('、')}`)
  exportDialogVisible.value = false
  selectedExportSystems.value = []
}

// 收集导出数据：每个测试用例使用最后查看的版本
const collectExportData = (systemIds: (string | number)[]) => {
  const exportData: any[] = []

  const collectFromTree = (nodes: TreeNode[], systemIds: (string | number)[]) => {
    for (const node of nodes) {
      // 如果是系统节点且在选中列表中
      if (node.type === 'system' && systemIds.includes(node.id)) {
        collectTestCasesFromSystem(node, exportData)
      }

      if (node.children && node.children.length > 0) {
        collectFromTree(node.children, systemIds)
      }
    }
  }

  collectFromTree(productLibrary.value || [], systemIds)
  return exportData
}

// 导出数据项接口
interface ExportDataItem {
  systemName: string
  functionName: string
  testItemName: string
  testCaseName: string
  identifier: string
  [key: string]: any
}

// 从系统节点收集所有测试用例数据
const collectTestCasesFromSystem = (systemNode: TreeNode, exportData: ExportDataItem[]): void => {
  const collectTestCases = (nodes: TreeNode[]) => {
    for (const node of nodes) {
      if (node.type === 'testCase') {
        // 使用当前用例的详情数据
        const usecaseDetail = node.usecaseDetail || createDefaultUsecaseDetail(node)

        // 获取父级节点信息
        const functionParent = findParentNode(productLibrary.value || [], node.id)
        const testItemParent = functionParent ? findParentNode(productLibrary.value || [], functionParent.id) : null

        exportData.push({
          systemName: systemNode.name,
          functionName: testItemParent?.name || '未知功能',
          testItemName: functionParent?.name || '未知测试项',
          testCaseName: node.name,
          identifier: usecaseDetail.identifier || generateIdentifier(node),
          testCaseId: node.id,
          versionId: 1,
          versionName: '当前版本',
          data: usecaseDetail,
        })
      }

      if (node.children && node.children.length > 0) {
        collectTestCases(node.children)
      }
    }
  }

  if (systemNode.children) {
    collectTestCases(systemNode.children)
  }
}

// 移除未使用的函数：addTableRow, removeTableRow, saveProduct, handleRowClick

// 生成标识符的方法
const generateIdentifier = (testCaseNode: TreeNode) => {
  if (!testCaseNode) return ''

  // 查找父级节点
  const findParentNodes = (
    node: TreeNode,
    tree: TreeNode[],
    parents: TreeNode[] = [],
  ): TreeNode[] => {
    for (const item of tree) {
      if (item.id === node.id) {
        return parents
      }
      if (item.children) {
        const result = findParentNodes(node, item.children, [...parents, item])
        if (result.length > 0 || result === parents) {
          return result.length > 0 ? result : [...parents, item]
        }
      }
    }
    return []
  }

  const parentNodes = findParentNodes(testCaseNode, productLibrary.value || [])

  if (parentNodes.length < 3) return '' // 需要至少3级父节点：系统、功能、测试项

  const systemNode = parentNodes[0] // 系统
  const functionNode = parentNodes[1] // 功能
  const testItemNode = parentNodes[2] // 测试项

  // 生成系统名称首字母缩写
  const systemAbbr = getSystemAbbreviation(systemNode.name)

  // 生成功能编号 (GN + 序号)
  const functionIndex =
    (systemNode.children?.findIndex((child: TreeNode) => child.id === functionNode.id) ?? -1) + 1 ||
    1
  const functionCode = `GN${functionIndex.toString().padStart(2, '0')}`

  // 生成测试项编号
  const testItemIndex =
    (functionNode.children?.findIndex((child: TreeNode) => child.id === testItemNode.id) ?? -1) +
      1 || 1
  const testItemCode = testItemIndex.toString().padStart(3, '0')

  // 生成用例编号
  const testCaseIndex = testItemNode.children
    ? testItemNode.children.findIndex((child: TreeNode) => child.id === testCaseNode.id) + 1 || 1
    : 1
  const testCaseCode = testCaseIndex.toString().padStart(3, '0')

  return `${systemAbbr}-${functionCode}-${testItemCode}-${testCaseCode}`
}

// 获取系统名称缩写
const getSystemAbbreviation = (systemName: string): string => {
  // 简单的缩写规则：取中文首字母或英文首字母
  if (systemName.includes('系统A')) return 'XTA'
  if (systemName.includes('系统B')) return 'XTB'
  if (systemName.includes('系统C')) return 'XTC'

  // 默认规则：取前3个字符的拼音首字母（这里简化处理）
  return 'XTA' // 默认值
}

// 处理测试用例名称变化
const handleTestCaseNameChange = () => {
  if (selectedTestCase.value && usecaseDetailForm.caseName) {
    // 检查是否是用户主动修改表单名称，还是选中节点时的自动同步
    const currentNodeName = selectedTestCase.value.name
    const parsed = parseTestCaseName(currentNodeName)

    // 如果表单名称与当前节点的自定义名称相同，说明是自动同步，不需要处理
    if (usecaseDetailForm.caseName === parsed.customName) {
      return
    }

    // 构建新名称：新的自定义名称-原序号
    const newName = `${usecaseDetailForm.caseName}-${parsed.sequence}`

    // 更新左侧树形结构中的节点名称
    selectedTestCase.value.name = newName

    // 更新用例详情中的测试用例名称
    if (selectedTestCase.value.usecaseDetail) {
      selectedTestCase.value.usecaseDetail.testCaseName = newName
    }

    // 重新生成标识符
    usecaseDetailForm.identifier = generateProductIdentifier(selectedTestCase.value)
  }
}

// 搜索相关方法
const handleSearch = () => {
  if (searchTarget.value === 'tree' && productTreeRef.value) {
    productTreeRef.value.filter(searchKeyword.value)
  }
}

const filterTreeNode = (value: string, data: TreeNode) => {
  if (!value) return true
  return data.name.toLowerCase().includes(value.toLowerCase())
}

/**
 * 异步更新用例顺序
 */
const updateCaseOrderAsync = async (sourceNode: TreeNode) => {
  try {
    // 找到源节点所属的系统
    const systemNode = findSystemForNode(sourceNode)
    if (!systemNode) {
      console.warn('无法找到用例所属的系统，跳过API调用')
      return
    }

    // 验证系统数据完整性
    if (!validateSystemDataIntegrity(systemNode)) {
      console.warn('系统数据完整性验证失败，跳过API调用')
      return
    }

    // 调试：检查系统节点的完整性
    console.log('🔍 系统节点详细信息:', {
      systemName: systemNode.name,
      systemKey: systemNode.systemKey,
      configCount: systemNode.children?.length || 0,
      configs: systemNode.children?.map(config => ({
        name: config.name,
        type: config.type,
        testItemCount: config.children?.length || 0,
        testItems: config.children?.map(testItem => ({
          name: testItem.name,
          type: testItem.type,
          caseCount: testItem.children?.length || 0
        }))
      }))
    })

    // 构建整个系统的数据结构
    const systemData = buildSystemDataStructure(systemNode)

    // 验证构建的数据结构
    if (!validateBuiltSystemData(systemData, systemNode)) {
      console.warn('构建的系统数据验证失败，跳过API调用')
      return
    }

    // 构建API请求数据
    const requestData = {
      caseListData: {
        [systemNode.name]: systemData
      }
    }

    // 详细记录请求数据用于调试
    console.log('📡 发送用例顺序更新请求:', {
      systemName: systemNode.name,
      systemKey: systemNode.systemKey,
      requestData: JSON.stringify(requestData, null, 2)
    })

    // 记录数据统计信息（需要包含产品库和备选库的用例）
    const candidateSystemNode = findCandidateSystemByKey(systemNode.systemKey || systemNode.name)
    const originalCaseCount = countCasesInMergedSystem(systemNode, candidateSystemNode)
    const builtCaseCount = countCasesInBuiltData(systemData)
    console.log('📊 详细用例统计:', {
      systemName: systemNode.name,
      originalCaseCount,
      builtCaseCount,
      difference: builtCaseCount - originalCaseCount,
      isConsistent: originalCaseCount === builtCaseCount
    })

    // 异步发送请求，不阻塞UI
    apiService.caseManagement.updateCaseOrder(requestData)
      .then(response => {
        console.log('✅ 用例顺序更新成功:', response)
      })
      .catch(error => {
        console.error('❌ 用例顺序更新失败:', error)
        // 检查是否是用例数量不一致错误
        if (error.message && error.message.includes('用例数量不一致')) {
          console.warn('🔍 检测到用例数量不一致错误，详细分析:')
          console.warn('原始系统节点:', systemNode)
          console.warn('构建的系统数据:', systemData)
          console.warn('请求数据:', requestData)

          // 尝试重新验证数据
          const revalidationResult = validateBuiltSystemData(systemData, systemNode)
          console.warn('重新验证结果:', revalidationResult)
        }
      })
  } catch (error) {
    console.error('❌ 构建用例顺序数据失败:', error)
    console.error('错误堆栈:', (error as Error).stack)
    console.error('系统节点信息:', {
      systemName: sourceNode.name,
      systemKey: sourceNode.systemKey,
      systemType: sourceNode.type,
      hasChildren: !!sourceNode.children,
      childrenCount: sourceNode.children?.length || 0
    })
  }
}

/**
 * 验证系统数据完整性
 */
const validateSystemDataIntegrity = (systemNode: TreeNode): boolean => {
  try {
    // 检查系统节点基本信息
    if (!systemNode || !systemNode.name || !systemNode.systemKey) {
      console.warn('系统节点缺少基本信息:', systemNode)
      return false
    }

    // 检查系统是否有子节点（配置项）
    if (!systemNode.children || systemNode.children.length === 0) {
      console.warn('系统节点没有配置项子节点:', systemNode.name)
      return true // 空系统也是有效的
    }

    // 验证每个配置项
    for (const configNode of systemNode.children) {
      if (configNode.type !== 'function') {
        continue
      }

      // 检查配置项是否有测试项
      if (configNode.children && configNode.children.length > 0) {
        for (const testItemNode of configNode.children) {
          if (testItemNode.type !== 'testItem') {
            continue
          }

          // 检查测试项是否有用例
          if (testItemNode.children && testItemNode.children.length > 0) {
            for (const caseNode of testItemNode.children) {
              if (caseNode.type === 'testCase') {
                // 验证用例节点的基本信息
                if (!caseNode.id || !caseNode.name) {
                  console.warn('用例节点缺少基本信息:', caseNode)
                  return false
                }
              }
            }
          }
        }
      }
    }

    return true
  } catch (error) {
    console.error('验证系统数据完整性时出错:', error)
    return false
  }
}

/**
 * 验证构建的系统数据
 */
const validateBuiltSystemData = (systemData: any, originalSystemNode: TreeNode): boolean => {
  try {
    // 检查基本结构
    if (!systemData || !systemData.data || !Array.isArray(systemData.path)) {
      console.warn('构建的系统数据结构不正确:', systemData)
      return false
    }

    // 统计原始系统中的用例数量（包含产品库和备选库）
    const candidateSystemNode = findCandidateSystemByKey(originalSystemNode.systemKey || originalSystemNode.name)
    const originalCaseCount = countCasesInMergedSystem(originalSystemNode, candidateSystemNode)

    // 统计构建数据中的用例数量
    const builtCaseCount = countCasesInBuiltData(systemData)

    console.log('📊 用例数量对比:', {
      original: originalCaseCount,
      built: builtCaseCount,
      systemName: originalSystemNode.name
    })

    // 检查用例数量是否一致
    if (originalCaseCount !== builtCaseCount) {
      console.warn('⚠️ 用例数量不一致:', {
        original: originalCaseCount,
        built: builtCaseCount,
        difference: builtCaseCount - originalCaseCount
      })
      return false
    }

    return true
  } catch (error) {
    console.error('验证构建的系统数据时出错:', error)
    return false
  }
}

/**
 * 统计合并系统（产品库+备选库）中的用例数量
 */
const countCasesInMergedSystem = (productSystemNode: TreeNode, candidateSystemNode: TreeNode | null): number => {
  let count = 0
  const details: any[] = []
  const caseSet = new Set<string>() // 用于去重

  const countInNode = (node: TreeNode, path: string[] = [], source: string) => {
    const currentPath = [...path, node.name]

    if (node.type === 'testCase') {
      const caseKey = (node as any).originalName || node.name
      if (!caseSet.has(caseKey)) {
        caseSet.add(caseKey)
        count++
        details.push({
          path: currentPath.join(' > '),
          caseId: node.id,
          caseName: node.name,
          caseKey: caseKey,
          selected: node.selected,
          source: source
        })
      }
    }

    if (node.children) {
      for (const child of node.children) {
        countInNode(child, currentPath, source)
      }
    }
  }

  // 统计产品库中的用例
  countInNode(productSystemNode, [], '产品库')

  // 统计备选库中的用例（去重）
  if (candidateSystemNode) {
    countInNode(candidateSystemNode, [], '备选库')
  }

  console.log('📊 合并系统中的用例统计详情:', {
    productSystemName: productSystemNode.name,
    candidateSystemName: candidateSystemNode?.name || '无',
    totalCount: count,
    details: details
  })

  return count
}

/**
 * 统计系统节点中的用例数量
 */
const countCasesInSystemNode = (systemNode: TreeNode): number => {
  let count = 0
  const details: any[] = []

  const countInNode = (node: TreeNode, path: string[] = []) => {
    const currentPath = [...path, node.name]

    if (node.type === 'testCase') {
      count++
      details.push({
        path: currentPath.join(' > '),
        caseId: node.id,
        caseName: node.name,
        selected: node.selected
      })
    }

    if (node.children) {
      for (const child of node.children) {
        countInNode(child, currentPath)
      }
    }
  }

  countInNode(systemNode)

  console.log('📊 原始系统节点中的用例统计详情:', {
    systemName: systemNode.name,
    totalCount: count,
    details: details
  })

  return count
}

/**
 * 统计构建数据中的用例数量
 */
const countCasesInBuiltData = (systemData: any): number => {
  let count = 0
  const details: any[] = []

  if (systemData.path && Array.isArray(systemData.path)) {
    for (const configItem of systemData.path) {
      for (const configKey in configItem) {
        const configData = configItem[configKey]
        if (configData.path && Array.isArray(configData.path)) {
          for (const testItem of configData.path) {
            for (const testKey in testItem) {
              const testData = testItem[testKey]
              if (testData.path && Array.isArray(testData.path)) {
                const testCaseCount = testData.path.length
                count += testCaseCount
                details.push({
                  config: configKey,
                  testItem: testKey,
                  caseCount: testCaseCount,
                  cases: testData.path.map((caseItem: any) => Object.keys(caseItem)[0])
                })
              }
            }
          }
        }
      }
    }
  }

  console.log('📊 构建数据中的用例统计详情:', {
    totalCount: count,
    details: details
  })

  return count
}

/**
 * 查找节点所属的系统
 */
const findSystemForNode = (node: TreeNode): TreeNode | null => {
  if (!productLibrary.value) return null

  for (const system of productLibrary.value) {
    if (findNodeInSystemTree(system, node.id)) {
      return system
    }
  }
  return null
}

/**
 * 根据系统键或名称查找备选库中的系统节点
 */
const findCandidateSystemByKey = (systemKey: string): TreeNode | null => {
  if (!candidateLibrary.value || !systemKey) return null

  for (const system of candidateLibrary.value) {
    if (system.systemKey === systemKey || system.name === systemKey) {
      return system
    }
  }
  return null
}

/**
 * 合并两个测试项节点（产品库和备选库）
 */
const mergeTestItemNodes = (productTestItemNode: TreeNode, candidateTestItemNode: TreeNode): TreeNode => {
  const mergedTestItemNode: TreeNode = {
    ...productTestItemNode,
    children: []
  }

  const caseMap = new Map<string, TreeNode>()

  // 首先添加产品库中的用例
  if (productTestItemNode.children) {
    for (const caseNode of productTestItemNode.children) {
      if (caseNode.type === 'testCase') {
        const caseKey = (caseNode as any).originalName || caseNode.name
        caseMap.set(caseKey, caseNode)
      }
    }
  }

  // 然后添加备选库中的用例（不重复）
  if (candidateTestItemNode.children) {
    for (const candidateCaseNode of candidateTestItemNode.children) {
      if (candidateCaseNode.type === 'testCase') {
        const caseKey = (candidateCaseNode as any).originalName || candidateCaseNode.name

        if (!caseMap.has(caseKey)) {
          // 只添加产品库中没有的用例
          caseMap.set(caseKey, candidateCaseNode)
        }
      }
    }
  }

  mergedTestItemNode.children = Array.from(caseMap.values())

  console.log('🔄 合并测试项:', {
    testItemName: productTestItemNode.name,
    productCaseCount: productTestItemNode.children?.length || 0,
    candidateCaseCount: candidateTestItemNode.children?.length || 0,
    mergedCaseCount: mergedTestItemNode.children.length
  })

  return mergedTestItemNode
}

/**
 * 合并两个配置项节点（产品库和备选库）
 */
const mergeConfigNodes = (productConfigNode: TreeNode, candidateConfigNode: TreeNode): TreeNode => {
  const mergedConfigNode: TreeNode = {
    ...productConfigNode,
    children: []
  }

  const testItemMap = new Map<string, TreeNode>()

  // 首先添加产品库中的测试项
  if (productConfigNode.children) {
    for (const testItemNode of productConfigNode.children) {
      if (testItemNode.type === 'testItem') {
        const testItemKey = (testItemNode as any).originalName || getOriginalNodeName(testItemNode.name)
        testItemMap.set(testItemKey, testItemNode)
      }
    }
  }

  // 然后合并备选库中的测试项
  if (candidateConfigNode.children) {
    for (const candidateTestItemNode of candidateConfigNode.children) {
      if (candidateTestItemNode.type === 'testItem') {
        const testItemKey = (candidateTestItemNode as any).originalName || getOriginalNodeName(candidateTestItemNode.name)

        if (testItemMap.has(testItemKey)) {
          // 如果产品库中已有此测试项，则合并用例
          const productTestItemNode = testItemMap.get(testItemKey)!
          const mergedTestItemNode = mergeTestItemNodes(productTestItemNode, candidateTestItemNode)
          testItemMap.set(testItemKey, mergedTestItemNode)
        } else {
          // 如果产品库中没有此测试项，直接添加备选库的测试项
          testItemMap.set(testItemKey, candidateTestItemNode)
        }
      }
    }
  }

  mergedConfigNode.children = Array.from(testItemMap.values())
  return mergedConfigNode
}

/**
 * 合并产品库和备选库中同一系统的配置项
 */
const mergeSystemConfigs = (productSystemNode: TreeNode, candidateSystemNode: TreeNode | null): TreeNode[] => {
  const configMap = new Map<string, TreeNode>()

  // 首先添加产品库中的配置项
  if (productSystemNode.children) {
    for (const configNode of productSystemNode.children) {
      if (configNode.type === 'function') {
        const configKey = (configNode as any).originalName || getOriginalNodeName(configNode.name)
        configMap.set(configKey, configNode)
      }
    }
  }

  // 然后合并备选库中的配置项
  if (candidateSystemNode?.children) {
    for (const candidateConfigNode of candidateSystemNode.children) {
      if (candidateConfigNode.type === 'function') {
        const configKey = (candidateConfigNode as any).originalName || getOriginalNodeName(candidateConfigNode.name)

        if (configMap.has(configKey)) {
          // 如果产品库中已有此配置项，则合并测试项和用例
          const productConfigNode = configMap.get(configKey)!
          const mergedConfigNode = mergeConfigNodes(productConfigNode, candidateConfigNode)
          configMap.set(configKey, mergedConfigNode)
        } else {
          // 如果产品库中没有此配置项，直接添加备选库的配置项
          configMap.set(configKey, candidateConfigNode)
        }
      }
    }
  }

  // 转换为数组并保持原有顺序
  return Array.from(configMap.values())
}



/**
 * 在系统树中查找指定节点
 */
const findNodeInSystemTree = (systemNode: TreeNode, targetId: string | number): boolean => {
  if (systemNode.id === targetId) return true

  if (systemNode.children) {
    for (const child of systemNode.children) {
      if (findNodeInSystemTree(child, targetId)) {
        return true
      }
    }
  }

  return false
}

/**
 * 构建系统数据结构（合并产品库和备选库的用例）
 */
const buildSystemDataStructure = (systemNode: TreeNode): any => {
  // 从原始数据中获取系统信息，确保字段值与获取目录结构接口响应数据保持一致
  let systemInfo = null
  if (originalSystemsData.value && systemNode.systemKey) {
    const systemData = originalSystemsData.value[systemNode.systemKey]

    // 检查系统数据的结构，可能需要访问data属性
    if (systemData && systemData.data && typeof systemData.data === 'object') {
      // 如果systemData有data属性，使用data属性中的信息
      systemInfo = systemData.data
      console.log('🔍 使用系统data属性:', systemInfo)
    } else if (systemData && typeof systemData === 'object') {
      // 如果systemData本身就是配置信息
      systemInfo = systemData
      console.log('🔍 使用系统数据本身:', systemInfo)
    }

    console.log('🔍 获取系统信息详情:', {
      systemKey: systemNode.systemKey,
      systemNode: systemNode,
      originalSystemsData: originalSystemsData.value,
      systemData: systemData,
      systemInfo: systemInfo,
      hasSubsystem: !!systemInfo?.subsystem,
      hasSoftwareName: !!systemInfo?.softwareName,
      allKeys: systemInfo ? Object.keys(systemInfo) : []
    })
  } else {
    console.warn('⚠️ 无法通过systemKey获取系统信息，尝试通过名称查找:', {
      hasOriginalData: !!originalSystemsData.value,
      systemKey: systemNode.systemKey,
      systemName: systemNode.name,
      availableKeys: originalSystemsData.value ? Object.keys(originalSystemsData.value) : []
    })

    // 备用方案：通过系统名称查找
    if (originalSystemsData.value && systemNode.name) {
      for (const [key, value] of Object.entries(originalSystemsData.value)) {
        if (value && typeof value === 'object') {
          // 检查是否有匹配的系统名称
          const systemData = value as any
          if (systemData.name === systemNode.name || key === systemNode.name) {
            if (systemData.data && typeof systemData.data === 'object') {
              systemInfo = systemData.data
              console.log('✅ 通过名称找到系统data:', systemInfo)
            } else if (systemData && typeof systemData === 'object') {
              systemInfo = systemData
              console.log('✅ 通过名称找到系统信息:', systemInfo)
            }
            break
          }
        }
      }
    }
  }

  // 最终备用方案：如果仍然没有找到系统信息，尝试从系统节点本身获取
  if (!systemInfo && systemNode) {
    console.log('🔄 使用系统节点本身的信息作为备用方案')
    systemInfo = {
      subsystem: (systemNode as any).subsystem || '',
      softwareName: (systemNode as any).softwareName || systemNode.name || '',
      softwareType: (systemNode as any).softwareType || '',
      securityLevel: (systemNode as any).securityLevel || '',
      runtimeEnv: (systemNode as any).runtimeEnv || '',
      developEnv: (systemNode as any).developEnv || '',
      language: (systemNode as any).language || '',
      version: (systemNode as any).version || '',
      codeTemplate: (systemNode as any).codeTemplate || '',
      developer: (systemNode as any).developer || ''
    }
  }

  const systemData = {
    data: {
      type: "system",
      subsystem: systemInfo?.subsystem || "",
      softwareName: systemInfo?.softwareName || "",
      softwareType: systemInfo?.softwareType || "",
      securityLevel: systemInfo?.securityLevel || "",
      runtimeEnv: systemInfo?.runtimeEnv || "",
      developEnv: systemInfo?.developEnv || "",
      language: systemInfo?.language || "",
      version: systemInfo?.version || "",
      codeTemplate: systemInfo?.codeTemplate || "",
      developer: systemInfo?.developer || ""
    },
    path: [] as any[]
  }

  // 查找备选库中对应的系统节点
  const candidateSystemNode = findCandidateSystemByKey(systemNode.systemKey || systemNode.name)

  console.log('🔍 查找备选库系统节点:', {
    productSystemName: systemNode.name,
    productSystemKey: systemNode.systemKey,
    candidateSystemFound: !!candidateSystemNode,
    candidateSystemName: candidateSystemNode?.name
  })

  // 构建配置项路径（合并产品库和备选库的配置项）
  const mergedConfigs = mergeSystemConfigs(systemNode, candidateSystemNode)

  console.log('🔄 合并后的配置项:', {
    productConfigCount: systemNode.children?.length || 0,
    candidateConfigCount: candidateSystemNode?.children?.length || 0,
    mergedConfigCount: mergedConfigs.length,
    mergedConfigDetails: mergedConfigs.map(config => ({
      name: config.name,
      type: config.type,
      testItemCount: config.children?.length || 0,
      testItems: config.children?.map(testItem => ({
        name: testItem.name,
        caseCount: testItem.children?.length || 0
      }))
    }))
  })

  if (mergedConfigs.length > 0) {
    for (const configNode of mergedConfigs) {
      if (configNode.type === 'function') {
        const configData = buildConfigNodeData(configNode, systemData.data)
        // 使用原始名称作为键，去除序号后缀
        const configName = (configNode as any).originalName || getOriginalNodeName(configNode.name)
        systemData.path.push({
          [configName]: configData
        })

        console.log(`📁 添加配置项 ${configName}:`, {
          testItemCount: configData.path?.length || 0,
          testItems: configData.path?.map((testItem: any) => {
            const testItemKey = Object.keys(testItem)[0]
            const testItemData = testItem[testItemKey]
            return {
              name: testItemKey,
              caseCount: testItemData.path?.length || 0
            }
          })
        })
      }
    }
  }

  return systemData
}

/**
 * 构建配置项节点数据
 */
const buildConfigNodeData = (configNode: TreeNode, systemData: any): any => {
  // 获取配置项的原始data字段
  let configDataField = null

  // 优先使用配置项节点的originalData字段
  if ((configNode as any).originalData) {
    configDataField = (configNode as any).originalData
    console.log('🔍 使用配置项originalData:', configDataField)
  }
  // 备用：从原始系统数据中获取配置项的data字段
  else if (originalSystemsData.value && configNode.systemKey) {
    const systemInfo = originalSystemsData.value[configNode.systemKey]
    if (systemInfo && systemInfo.path) {
      // 在系统的path中查找当前配置项
      for (const pathItem of systemInfo.path) {
        const configName = (configNode as any).originalName || configNode.name
        if (pathItem[configName] && pathItem[configName].data) {
          configDataField = pathItem[configName].data
          console.log('🔍 从原始系统数据获取配置项data:', configDataField)
          break
        }
      }
    }
  }

  // 如果没有找到配置项的data字段，使用默认值
  if (!configDataField) {
    configDataField = {
      type: "config",
      subsystem: "",
      softwareName: "",
      softwareType: "",
      securityLevel: "",
      runtimeEnv: "",
      developEnv: "",
      language: "",
      version: "",
      codeTemplate: "",
      developer: ""
    }
    console.log('⚠️ 使用默认配置项data字段:', configDataField)
  }

  const configData = {
    data: configDataField, // 使用配置项自己的data字段
    path: [] as any[],
    tableName: configNode.tableName || ""
  }

  // 构建测试项路径
  if (configNode.children) {
    for (const testItemNode of configNode.children) {
      if (testItemNode.type === 'testItem') {
        const testItemData = buildTestItemNodeData(testItemNode, systemData)
        // 使用原始名称作为键，去除序号后缀
        const testItemName = (testItemNode as any).originalName || getOriginalNodeName(testItemNode.name)
        configData.path.push({
          [testItemName]: testItemData
        })
      }
    }
  }

  return configData
}

/**
 * 构建测试项节点数据
 */
const buildTestItemNodeData = (testItemNode: TreeNode, _systemData: any): any => {
  const testItemData = {
    data: {
      type: "testMethod" // 所有测试项都使用固定的data字段结构
    },
    path: [] as any[]
  }

  // 构建用例路径 - 确保所有用例都被包含，避免重复或遗漏
  if (testItemNode.children) {
    console.log('🔍 构建测试项数据:', {
      testItemName: testItemNode.name,
      childrenCount: testItemNode.children.length,
      children: testItemNode.children.map(c => ({ id: c.id, name: c.name, type: c.type, selected: c.selected }))
    })

    // 使用Map来避免重复用例
    const caseMap = new Map<string, TreeNode>()

    for (const caseNode of testItemNode.children) {
      if (caseNode.type === 'testCase') {
        // 使用用例ID作为唯一标识，避免重复
        const caseKey = caseNode.id || caseNode.name
        if (!caseMap.has(caseKey)) {
          caseMap.set(caseKey, caseNode)
        } else {
          console.warn('⚠️ 发现重复用例，跳过:', {
            id: caseNode.id,
            name: caseNode.name,
            existing: caseMap.get(caseKey)
          })
        }
      }
    }

    // 按照用例的顺序添加到path中
    const sortedCases = Array.from(caseMap.values()).sort((a, b) => {
      // 优先按照在children数组中的位置排序
      const indexA = testItemNode.children!.findIndex(c => c.id === a.id)
      const indexB = testItemNode.children!.findIndex(c => c.id === b.id)
      return indexA - indexB
    })

    for (const caseNode of sortedCases) {
      const caseName = (caseNode as any).originalName || caseNode.name

      // 确保用例名称唯一性
      const existingCase = testItemData.path.find(item => Object.keys(item)[0] === caseName)
      if (existingCase) {
        console.warn('⚠️ 发现重复用例名称，添加后缀:', caseName)
        const uniqueName = `${caseName}_${caseNode.id}`
        testItemData.path.push({
          [uniqueName]: {
            id: caseNode.id,
            selected: caseNode.selected !== false,
            deleted: caseNode.deleted || false
          }
        })
      } else {
        testItemData.path.push({
          [caseName]: {
            id: caseNode.id,
            selected: caseNode.selected !== false,
            deleted: caseNode.deleted || false
          }
        })
      }
    }

    console.log('✅ 测试项数据构建完成:', {
      testItemName: testItemNode.name,
      originalCaseCount: testItemNode.children.filter(c => c.type === 'testCase').length,
      builtCaseCount: testItemData.path.length,
      cases: testItemData.path.map(item => Object.keys(item)[0])
    })
  }

  return testItemData
}

// ============================================================================
// 备选库管理函数
// ============================================================================

/**
 * 一键移动备选库用例到产品库
 */
const moveCaseToProduct = async (caseNode: TreeNode) => {
  try {
    console.log('🔄 开始移动用例到产品库:', caseNode)

    // 检查是否有必要的信息
    if (!caseNode.id || !caseNode.tableName) {
      ElMessage.error('用例信息不完整，无法移动')
      return
    }

    // 找到对应的系统名称
    const systemName = findSystemNameForCase(caseNode)
    if (!systemName) {
      ElMessage.error('无法确定用例所属系统')
      return
    }

    // 调用API选择用例（移动到产品库）
    const selectionData = {
      systemName: systemName,
      tableId: caseNode.tableName,
      caseId: caseNode.id.toString(),
      selected: true
    }

    console.log('📡 发送用例选择请求:', selectionData)

    const response = await apiService.caseManagement.selectCase(selectionData)

    if (response) {
      ElMessage.success('用例已成功移动到产品库')

      // 刷新数据
      await fetchSystems()

      // 清除当前选中的备选库用例
      selectedCandidateTestCase.value = null
      resetCandidateDetailForm()

      console.log('✅ 用例移动成功:', response)
    } else {
      throw new Error('API响应异常')
    }
  } catch (error: any) {
    console.error('❌ 移动用例失败:', error)
    ElMessage.error('移动用例失败：' + (error.message || '未知错误'))
  }
}

/**
 * 根据用例节点查找系统名称
 */
const findSystemNameForCase = (caseNode: TreeNode): string | null => {
  // 从备选库中查找包含该用例的系统
  if (!candidateLibrary.value) return null

  for (const system of candidateLibrary.value) {
    if (findNodeInTree([system], caseNode.id)) {
      return system.name || system.systemKey || null
    }
  }

  return null
}

/**
 * 在树中查找指定ID的节点
 */
const findNodeInTree = (nodes: TreeNode[], targetId: string | number): boolean => {
  for (const node of nodes) {
    if (node.id === targetId) {
      return true
    }
    if (node.children && findNodeInTree(node.children, targetId)) {
      return true
    }
  }
  return false
}

const handleCandidateNodeClick = async (data: TreeNode) => {
  console.log('🖱️ 备选库节点点击:', {
    name: data.name,
    type: data.type,
    id: data.id,
    tableName: data.tableName,
    originalName: data.originalName,
    systemKey: data.systemKey,
    fullData: data
  })

  // 检查当前备选库表单是否有未保存的修改
  if (isCandidateFormModified.value) {
    try {
      await ElMessageBox.confirm(
        '当前备选库用例有未保存的修改，请将用例拖动到产品库并保存数据。是否继续切换？',
        '提示',
        {
          confirmButtonText: '继续切换',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )
    } catch {
      // 用户取消，不进行切换
      return
    }
  }

  selectedCandidateNode.value = data

  // 如果是测试项节点，并且浏览器已连接，则设置默认用例路径
  if (data.type === 'testItem' && isConnected.value) {
    console.log('🔗 备选库测试项节点点击，准备发送接口请求:', {
      nodeName: data.name,
      tableName: data.tableName,
      isConnected: isConnected.value,
      connectionState: connectionState,
      connectionStateConnected: connectionState.connected,
      connectionStateStatus: connectionState.status
    })

    try {
      // 获取测试项的父节点（配置项）来获取tableName
      const findParentNodes = (
        node: TreeNode,
        tree: TreeNode[],
        parents: TreeNode[] = [],
      ): TreeNode[] => {
        for (const item of tree) {
          if (item.id === node.id) {
            return parents
          }
          if (item.children) {
            const result = findParentNodes(node, item.children, [...parents, item])
            if (result.length > 0) {
              return result
            }
          }
        }
        return []
      }

      const parentNodes = findParentNodes(data, candidateLibrary.value || [])
      console.log('🔍 测试项父节点信息:', {
        testItemName: data.name,
        parentNodes: parentNodes.map(p => ({ name: p.name, type: p.type, tableName: p.tableName }))
      })

      // 获取配置项的tableName（父节点应该是配置项）
      let tableName = data.tableName || ''
      if (!tableName && parentNodes.length > 1) {
        // 父节点[1]应该是配置项
        const configNode = parentNodes[1]
        if (configNode && configNode.tableName) {
          tableName = configNode.tableName
          console.log('✅ 从父配置项获取tableName:', tableName)
        }
      }

      // 设置默认用例路径
      const pathData = {
        tableName: tableName,
        testMethod: (data as any).originalName || data.name || '' // 使用原始测试项名称
      }

      // 验证tableName是否有效
      if (!pathData.tableName) {
        console.error('❌ tableName为空，无法设置默认路径:', pathData)
        ElMessage.error('无法设置默认路径：缺少配置项信息')
        return
      }

      console.log('📡 发送设置默认用例路径请求:', pathData)
      const response = await apiService.caseManagement.setDefaultCasePath(pathData)
      console.log('📡 设置默认用例路径响应:', response)

      // 检查响应是否成功 - 适配实际的API响应格式
      const isSuccess = response && (
        response.success === true || // 标准格式
        ((response as any).userId && (response as any).defaultCasePath) || // 实际API格式
        ((response as any).status === 'success') // 备用格式
      )

      if (isSuccess) {
        // 更新选中状态
        selectedDefaultPath.value = pathData
        ElMessage.success('默认用例路径设置成功')
        console.log('✅ 默认用例路径设置成功')
      } else {
        ElMessage.error('默认用例路径设置失败')
        console.error('❌ 默认用例路径设置失败，响应:', response)
      }
    } catch (error: any) {
      console.error('❌ 设置默认用例路径失败:', error)
      ElMessage.error('设置默认用例路径失败: ' + (error.message || '未知错误'))
    }
  } else if (data.type === 'testItem') {
    console.log('⚠️ 备选库测试项节点点击，但浏览器未连接:', {
      nodeName: data.name,
      isConnected: isConnected.value,
      connectionState: connectionState,
      connectionStateConnected: connectionState.connected,
      connectionStateStatus: connectionState.status
    })

    // 尝试刷新浏览器连接状态
    console.log('🔄 尝试刷新浏览器连接状态...')
    await checkAndRefreshStatus()
    console.log('🔄 刷新后的连接状态:', {
      isConnected: isConnected.value,
      connectionState: connectionState
    })

    // 如果刷新后连接状态为true，重新尝试设置默认路径
    if (isConnected.value) {
      console.log('✅ 刷新后浏览器已连接，重新尝试设置默认路径')
      // 递归调用自己，但这次应该会进入连接分支
      return handleCandidateNodeClick(data)
    }
  }

  if (data.type === 'testCase') {
    console.log('📋 这是一个测试用例，开始加载数据...')
    selectedCandidateTestCase.value = data
    await loadCandidateUsecaseDetail(data)
    // 加载截图选中状态
    loadCandidateScreenshotSelection()

    // 确保备选库用例详情区域可见
    nextTick(() => {
      // 滚动到详情区域
      const detailElement = document.querySelector('.candidate-detail-form')
      if (detailElement) {
        detailElement.scrollIntoView({ behavior: 'smooth', block: 'nearest' })
      }
    })
  } else {
    selectedCandidateTestCase.value = null
    // 清空截图选中状态
    screenshotState.candidateSelectedIndices = []
    screenshotState.candidateSelectionOrder.clear()
    // 重置备选库详情表单
    resetCandidateDetailForm()
  }
}

// 重置备选库详情表单
const resetCandidateDetailForm = () => {
  Object.assign(candidateDetailForm, {
    testCaseName: '',
    identifier: '',
    traceRelation: '',
    testCaseSummary: '',
    usecaseInitialization: '',
    prerequisitesConstraints: '',
    testMethod: '',
    testSteps: [],
    testCaseTerminationCondition: '',
    testCasePassCriteria: '',
    executionStatus: '',
    executionResult: '',
    issueIdentification: '',
    designer: '',
    reviewer: '',
    testTime: '',
  })
}

const toggleCandidateTreeCollapse = () => {
  candidateLibraryCollapsed.value = !candidateLibraryCollapsed.value
}

// 查找备选库父节点
const findCandidateParentNode = (nodes: TreeNode[], targetId: number | string): TreeNode | null => {
  for (const node of nodes) {
    if (node.children) {
      for (const child of node.children) {
        if (child.id === targetId) {
          return node
        }
      }
      const found = findCandidateParentNode(node.children, targetId)
      if (found) {
        return found
      }
    }
  }
  return null
}

// 重新排序备选库同级节点
const reorderCandidateSiblings = (siblings: TreeNode[], nodeType: string) => {
  if (!siblings || siblings.length === 0) {
    return
  }

  // 获取同类型的节点
  const sameTypeNodes = siblings.filter((node) => node.type === nodeType)

  // 只对测试用例重新编号，其他节点类型保持原始名称
  if (nodeType === 'testCase') {
    sameTypeNodes.forEach((node, index) => {
      const sequenceNumber = index + 1
      // 测试用例：保持自定义名称，只更新序号
      node.name = rebuildTestCaseName(node.name, sequenceNumber)

      // 同时更新usecaseDetail中的testCaseName，保持与node.name一致
      if (node.usecaseDetail) {
        node.usecaseDetail.testCaseName = node.name
      }
    })
  }
  // 配置项和测试项不需要重新编号，保持原始名称
}

const addCandidateTreeNode = (data: TreeNode, node: ElTreeNode): void => {
  const newNodeId = Date.now()
  let newNode: TreeNode = {
    id: '',
    name: '',
    displayName: '',
    type: 'testCase',
  }

  switch (data.type) {
    case 'system':
      // 在系统下添加功能
      newNode = {
        id: `candidate-function-${newNodeId}`,
        name: '新建功能',
        displayName: '新建功能',
        type: 'function',
        children: [],
      }
      break
    case 'function':
      // 在功能下添加测试项
      newNode = {
        id: `candidate-testitem-${newNodeId}`,
        name: '新建测试项',
        displayName: '新建测试项',
        type: 'testItem',
        children: [],
      }
      break
    case 'testItem':
      // 在测试项下添加用例
      newNode = {
        id: `candidate-testcase-${newNodeId}`,
        name: '新建用例',
        displayName: '新建用例',
        type: 'testCase',
        description: '备选库测试用例',
        usecaseDetail: {
          testCaseName: `用例-1`, // 临时名称，稍后重新编号
          identifier: '', // 将在节点添加后生成
          traceRelation: '',
          testCaseSummary: '',
          usecaseInitialization: '',
          prerequisitesConstraints: '',
          testMethod: '',
          testSteps: [
            {
              stepNo: '1',
              inputAction: '',
              expectedResult: '',
              actualResult: '',
              evaluationCriteria: '',
              testConclusion: '',
            },
          ],
          testCaseTerminationCondition: '',
          testCasePassCriteria: '',
          executionStatus: '',
          executionResult: '',
          issueIdentification: '',
          designer: '',
          reviewer: '',
          testTime: '',
        },
      }

      // 如果有选中的测试用例，插入到其下方
      if (selectedCandidateTestCase.value && selectedCandidateTestCase.value.type === 'testCase') {
        const selectedParent = findCandidateParentNode(
          candidateLibrary.value || [],
          selectedCandidateTestCase.value.id,
        )
        if (selectedParent && selectedParent.id === data.id) {
          // 选中的用例在当前测试项下，插入到选中用例的下方
          const selectedIndex =
            data.children?.findIndex(
              (child: TreeNode) => child.id === selectedCandidateTestCase.value?.id,
            ) ?? -1
          if (selectedIndex !== -1 && data.children) {
            data.children.splice(selectedIndex + 1, 0, newNode)

            // 重新编号所有测试用例
            reorderCandidateSiblings(data.children, 'testCase')

            // 生成正确的标识
            if (newNode.type === 'testCase' && newNode.usecaseDetail) {
              newNode.usecaseDetail.identifier = generateCandidateIdentifier(newNode)
            }

            ElMessage.success(
              `已在 ${selectedCandidateTestCase.value.name} 下方添加${getNodeTypeName(newNode.type)}`,
            )
            return
          }
        }
      }
      break
    case 'testCase':
      // 在用例同级添加用例
      const candidateParent = findCandidateParentNode(candidateLibrary.value || [], data.id)
      if (candidateParent) {
        newNode = {
          id: `candidate-testcase-${newNodeId}`,
          name: `${getBaseName('testCase')}-1`, // 临时名称，稍后重新编号
          displayName: `${getBaseName('testCase')}-1`,
          type: 'testCase',
          description: '备选库测试用例',
          usecaseDetail: {
            testCaseName: `用例-1`, // 临时名称，稍后重新编号
            identifier: '', // 将在节点添加后生成
            traceRelation: '',
            testCaseSummary: '',
            usecaseInitialization: '',
            prerequisitesConstraints: '',
            testMethod: '',
            testSteps: [
              {
                stepNo: '1',
                inputAction: '',
                expectedResult: '',
                actualResult: '',
                evaluationCriteria: '',
                testConclusion: '',
              },
            ],
            testCaseTerminationCondition: '',
            testCasePassCriteria: '',
            executionStatus: '',
            executionResult: '',
            issueIdentification: '',
            designer: '',
            reviewer: '',
            testTime: '',
          },
        }

        // 找到当前用例在父节点children中的位置，插入到其下方
        const currentIndex =
          candidateParent.children?.findIndex((child: TreeNode) => child.id === data.id) ?? -1
        if (currentIndex !== -1 && candidateParent.children) {
          candidateParent.children.splice(currentIndex + 1, 0, newNode)

          // 重新编号所有测试用例
          reorderCandidateSiblings(candidateParent.children, 'testCase')

          // 生成正确的标识
          if (newNode.type === 'testCase' && newNode.usecaseDetail) {
            newNode.usecaseDetail.identifier = generateCandidateIdentifier(newNode)
          }

          ElMessage.success(`已在 ${data.name} 下方添加${getNodeTypeName(newNode.type)}`)
        } else {
          // 如果找不到位置，则添加到末尾
          if (!candidateParent.children) {
            candidateParent.children = []
          }
          candidateParent.children.push(newNode)

          // 重新编号所有测试用例
          reorderCandidateSiblings(candidateParent.children, 'testCase')

          // 生成正确的标识
          if (newNode.type === 'testCase' && newNode.usecaseDetail) {
            newNode.usecaseDetail.identifier = generateCandidateIdentifier(newNode)
          }

          ElMessage.success(`添加${getNodeTypeName(newNode.type)}成功`)
        }
        return
      }
      break
  }

  if (!data.children) {
    data.children = []
  }
  data.children.push(newNode)

  // 如果是测试用例，重新编号并生成正确的标识
  if (newNode.type === 'testCase') {
    reorderCandidateSiblings(data.children, 'testCase')
    if (newNode.usecaseDetail) {
      newNode.usecaseDetail.identifier = generateCandidateIdentifier(newNode)
    }
  }

  // 立即进入编辑模式，传递父节点信息
  nextTick(() => {
    startEditNodeName(newNode, newNode.name, data)
  })
}

const deleteCandidateTreeNode = (data: TreeNode, node: ElTreeNode): void => {
  const parent = node.parent
  const children = parent?.data?.children || parent?.data

  // 确保 children 是数组类型
  if (Array.isArray(children)) {
    const index = children.findIndex((d: TreeNode) => d.id === data.id)
    if (index !== -1) {
      children.splice(index, 1)
      ElMessage.success(`删除${getNodeTypeName(data.type)}成功`)

      // 如果删除的是当前选中的节点，清除选择
      if (selectedCandidateNode.value?.id === data.id) {
        selectedCandidateNode.value = null
        selectedCandidateTestCase.value = null
      }
    }
  } else {
    ElMessage.error('无法删除节点：找不到父节点')
  }
}

const handleCandidateSearch = () => {
  if (candidateSearchTarget.value === 'tree' && candidateTreeRef.value) {
    candidateTreeRef.value.filter(candidateSearchKeyword.value)
  }
  // 表格搜索逻辑已在计算属性中处理
}

// 库区域折叠功能
const toggleProductLibraryCollapse = () => {
  productLibraryCollapsed.value = !productLibraryCollapsed.value
  // 如果两个库都折叠了，则展开另一个
  if (productLibraryCollapsed.value && candidateLibraryCollapsed.value) {
    candidateLibraryCollapsed.value = false
  }
  // 延迟执行高度同步，等待过渡动画完成
  setTimeout(syncLibrariesHeight, 350)
}

const toggleCandidateLibraryCollapse = () => {
  candidateLibraryCollapsed.value = !candidateLibraryCollapsed.value
  // 如果两个库都折叠了，则展开另一个
  if (productLibraryCollapsed.value && candidateLibraryCollapsed.value) {
    productLibraryCollapsed.value = false
  }
  // 延迟执行高度同步，等待过渡动画完成
  setTimeout(syncLibrariesHeight, 350)
}

// 动态高度同步机制
// 使用已存在的 productLibraryRef 和 candidateLibraryRef

const syncLibrariesHeight = () => {
  if (!productLibraryRef.value || !candidateLibraryRef.value) return

  // 如果有库被折叠，则不进行高度同步
  if (productLibraryCollapsed.value || candidateLibraryCollapsed.value) return

  // 重置高度，让内容自然扩展
  productLibraryRef.value.style.height = 'auto'
  candidateLibraryRef.value.style.height = 'auto'
  productLibraryRef.value.style.minHeight = '600px'
  candidateLibraryRef.value.style.minHeight = '600px'

  // 等待DOM更新后获取实际高度
  nextTick(() => {
    if (!productLibraryRef.value || !candidateLibraryRef.value) return

    const productHeight = productLibraryRef.value.offsetHeight
    const candidateHeight = candidateLibraryRef.value.offsetHeight

    // 以较高的那个为基准，确保两个库高度一致
    const maxHeight = Math.max(productHeight, candidateHeight, 600) // 最小600px

    productLibraryRef.value.style.height = `${maxHeight}px`
    candidateLibraryRef.value.style.height = `${maxHeight}px`
  })
}

// 监听内容变化，自动同步高度 - 将在变量声明后初始化

// 监听折叠状态变化，重新同步高度
watch([productLibraryCollapsed, candidateLibraryCollapsed], () => {
  setTimeout(() => {
    syncLibrariesHeight()
  }, 350) // 等待折叠动画完成
})

// 拖拽事件处理函数
const handleMouseDown = (event: MouseEvent, node: TreeNode) => {
  // 只处理备选库中的用例节点
  if (node.type !== 'testCase') return

  dragState.dragStartTime = Date.now()
  dragState.longPressTimer = setTimeout(() => {
    startDrag(event, node)
  }, LONG_PRESS_DURATION) as any
}

const handleMouseUp = () => {
  if (dragState.longPressTimer) {
    clearTimeout(dragState.longPressTimer)
    dragState.longPressTimer = null
  }
}

const handleMouseMove = () => {
  if (dragState.longPressTimer) {
    clearTimeout(dragState.longPressTimer)
    dragState.longPressTimer = null
  }
}

const startDrag = (event: MouseEvent, node: TreeNode) => {
  dragState.isDragging = true
  dragState.draggedNode = node

  // 创建拖拽数据
  const dragData = {
    sourceType: 'candidate',
    nodeId: node.id,
    nodeData: JSON.parse(JSON.stringify(node)), // 深拷贝节点数据
  }

  // 设置拖拽效果
  const target = event.target as HTMLElement
  target.setAttribute('draggable', 'true')

  // 添加拖拽开始事件监听
  target.addEventListener('dragstart', (e) => {
    if (e.dataTransfer) {
      e.dataTransfer.setData('text/plain', JSON.stringify(dragData))
      e.dataTransfer.effectAllowed = 'move'
    }
  })

  // 添加拖拽结束事件监听
  target.addEventListener('dragend', () => {
    endDrag()
  })

  // 触发拖拽开始
  const dragEvent = new DragEvent('dragstart', {
    bubbles: true,
    cancelable: true,
    dataTransfer: new DataTransfer(),
  })
  target.dispatchEvent(dragEvent)
}

const endDrag = () => {
  dragState.isDragging = false
  dragState.draggedNode = null
  dragState.dragOverNode = null

  // 移除所有拖拽相关的属性
  const draggableElements = document.querySelectorAll('[draggable="true"]')
  draggableElements.forEach((el) => {
    el.removeAttribute('draggable')
  })
}

// 处理拖拽进入产品库节点
const handleDragEnter = (event: DragEvent, node: TreeNode): void => {
  event.preventDefault()
  if (node.type === 'testCase' || node.type === 'testItem') {
    dragState.dragOverNode = node
  }
}

// 处理拖拽在产品库节点上方
const handleDragOver = (event: DragEvent, node: TreeNode): void => {
  event.preventDefault()
  if (node.type === 'testCase' || node.type === 'testItem') {
    event.dataTransfer!.dropEffect = 'move'
  }
}

// 处理拖拽离开产品库节点
const handleDragLeave = (event: DragEvent): void => {
  event.preventDefault()
  dragState.dragOverNode = null
}

// 处理拖拽放置到产品库节点
const handleDrop = (event: DragEvent, targetNode: TreeNode) => {
  event.preventDefault()

  try {
    const dragDataStr = event.dataTransfer?.getData('text/plain')
    if (!dragDataStr) return

    const dragData = JSON.parse(dragDataStr)
    if (dragData.sourceType !== 'candidate') return

    // 执行拖拽迁移
    performDragMigration(dragData, targetNode)
  } catch (error) {
    ElMessage.error('拖拽操作失败')
  } finally {
    endDrag()
  }
}

// 执行拖拽迁移逻辑
const performDragMigration = async (dragData: any, targetNode: TreeNode) => {
  const sourceNode = dragData.nodeData

  // 只允许用例节点拖拽
  if (sourceNode.type !== 'testCase') {
    ElMessage.warning('只能拖拽用例节点')
    return
  }

  // 只允许拖拽到用例节点或测试项节点
  if (targetNode.type !== 'testCase' && targetNode.type !== 'testItem') {
    ElMessage.warning('只能拖拽到用例节点或测试项节点')
    return
  }

  try {
    // 创建新的用例节点（迁移到产品库）
    const newNode = createMigratedNode(sourceNode, targetNode)

    // 插入到目标位置
    insertNodeAtTarget(newNode, targetNode)

    // 调用API更新用例状态（从备选库移动到产品库）
    await moveCaseToLibrary(sourceNode, 'candidate', 'product')

    // 拖动移动需要同时发送目录结构更新接口
    updateCaseOrderAsync(newNode)

    // 从备选库中移除源节点
    removeFromCandidateLibrary(sourceNode.id)

    // 重新编号相关节点
    updateDraggedNodeSequence(newNode, targetNode)

    // 自动选中新迁移的用例
    selectedTestCase.value = newNode
    handleProductNodeClick(newNode)

    // 强制更新界面
    nextTick(() => {
      // 确保界面反映最新的节点名称
      if (newNode.usecaseDetail) {
        usecaseDetailForm.caseName = newNode.name
      }
      // 刷新数据以确保状态同步
      refreshSystems()
    })

    ElMessage.success(`已将 "${sourceNode.name}" 迁移到产品库，新名称为 "${newNode.name}"`)
  } catch (error) {
    console.error('拖拽迁移失败:', error)
    ElMessage.error('拖拽迁移失败，请重试')
    // 刷新数据以恢复状态
    refreshSystems()
  }
}

// 创建迁移后的节点
const createMigratedNode = (sourceNode: TreeNode, targetNode: TreeNode): TreeNode => {
  // 解析源节点名称，提取自定义部分
  const parsed = parseTestCaseName(sourceNode.name)
  let customName = parsed.hasCustomName ? parsed.customName : '用例'

  // 如果自定义名称就是"用例"，则使用默认的"用例"
  if (customName === '用例') {
    customName = '用例'
  }

  const newNode: TreeNode = {
    id: generateNewId(),
    name: `${customName}-1`, // 临时名称，会在reorderSiblings中重新分配正确序号
    displayName: `${customName}-1`,
    type: 'testCase',
    description: sourceNode.description || '',
    usecaseDetail: sourceNode.usecaseDetail
      ? JSON.parse(JSON.stringify(sourceNode.usecaseDetail))
      : createDefaultUsecaseDetail(sourceNode),
  }

  // 更新用例详情中的名称（会在后续更新中修正）
  if (newNode.usecaseDetail) {
    newNode.usecaseDetail.testCaseName = newNode.name
  }

  return newNode
}

// 插入节点到目标位置
const insertNodeAtTarget = (newNode: TreeNode, targetNode: TreeNode) => {
  if (targetNode.type === 'testCase') {
    // 拖拽到用例节点：插入到目标用例下方
    const parent = findParentNode(productLibrary.value || [], targetNode.id)
    if (parent && parent.children) {
      const targetIndex = parent.children.findIndex((child: TreeNode) => child.id === targetNode.id)
      parent.children.splice(targetIndex + 1, 0, newNode)
    }
  } else if (targetNode.type === 'testItem') {
    // 拖拽到测试项节点：插入到该测试项下所有用例的最后
    if (!targetNode.children) {
      targetNode.children = []
    }
    targetNode.children.push(newNode)
  }
}

// 调用API移动用例到指定库（使用与一键移动相同的接口）
const moveCaseToLibrary = async (sourceNode: TreeNode, fromLibrary: 'product' | 'candidate', toLibrary: 'product' | 'candidate') => {
  try {
    // 只处理从备选库移动到产品库的情况
    if (fromLibrary !== 'candidate' || toLibrary !== 'product') {
      console.warn('当前只支持从备选库移动到产品库')
      return
    }

    // 检查是否有必要的信息
    if (!sourceNode.id || !sourceNode.tableName) {
      throw new Error('用例信息不完整，无法移动')
    }

    // 找到对应的系统名称
    const systemName = findSystemNameForCase(sourceNode)
    if (!systemName) {
      throw new Error('无法确定用例所属系统')
    }

    // 调用用例选择API（与一键移动使用相同接口）
    const selectionData = {
      systemName: systemName,
      tableId: sourceNode.tableName,
      caseId: sourceNode.id.toString(),
      selected: true
    }

    console.log('📡 拖拽移动 - 发送用例选择请求:', selectionData)

    const response = await apiService.caseManagement.selectCase(selectionData)

    if (response) {
      console.log('✅ 拖拽移动成功:', response)
      return response
    } else {
      throw new Error('API响应异常')
    }
  } catch (error) {
    console.error('❌ 拖拽移动失败:', error)
    throw error
  }
}

// 从备选库中移除节点
const removeFromCandidateLibrary = (nodeId: string | number) => {
  const removeNodeRecursive = (nodes: TreeNode[]): boolean => {
    for (let i = 0; i < nodes.length; i++) {
      const node = nodes[i]
      if (node.id === nodeId) {
        nodes.splice(i, 1)
        return true
      }
      if (node.children && removeNodeRecursive(node.children)) {
        return true
      }
    }
    return false
  }

  removeNodeRecursive(candidateLibrary.value || [])
}

// 更新拖拽节点的序号和标识符
const updateDraggedNodeSequence = (newNode: TreeNode, targetNode: TreeNode) => {
  let parentNode: TreeNode | null = null
  let targetChildren: TreeNode[] = []

  if (targetNode.type === 'testCase') {
    // 拖拽到用例节点：找到父测试项
    parentNode = findParentNode(productLibrary.value || [], targetNode.id)
    if (parentNode && parentNode.children) {
      targetChildren = parentNode.children
    }
  } else if (targetNode.type === 'testItem') {
    // 拖拽到测试项节点：直接使用该测试项
    parentNode = targetNode
    targetChildren = targetNode.children || []
  }

  if (parentNode && targetChildren.length > 0) {
    // 重新排序所有用例节点
    reorderSiblings(targetChildren, 'testCase')

    // 重新生成所有用例的标识符和更新用例详情
    targetChildren.forEach((child: TreeNode) => {
      if (child.type === 'testCase') {
        // 更新用例详情中的名称
        if (child.usecaseDetail) {
          child.usecaseDetail.testCaseName = child.name
          child.usecaseDetail.identifier = generateIdentifier(child)
        }
      }
    })
  }

  // 更新备选库中剩余用例的序号
  updateCandidateSequenceNumbers()
}

// 更新备选库序号
const updateCandidateSequenceNumbers = () => {
  const updateNodeRecursive = (nodes: TreeNode[]) => {
    nodes.forEach((node) => {
      if (node.children) {
        // 重新编号同级的测试用例
        reorderCandidateSiblings(node.children, 'testCase')
        updateNodeRecursive(node.children)
      }
    })
  }

  updateNodeRecursive(candidateLibrary.value || [])
}

// ============================================================================
// 拖拽功能管理
// ============================================================================

// 产品库内部拖拽事件处理
const handleProductMouseDown = (event: MouseEvent, node: TreeNode) => {
  if (node.type !== 'testCase') return

  productDragState.canDrag = true
  event.stopPropagation()
}

const handleProductMouseUp = () => {
  setTimeout(() => {
    if (!productDragState.isDragging) {
      productDragState.canDrag = false
    }
  }, 50)
}

const handleProductMouseMove = (event: MouseEvent) => {
  // 鼠标移动时的处理逻辑
}

const handleProductTreeDragStart = (event: DragEvent, node: TreeNode) => {
  if (node.type !== 'testCase') {
    event.preventDefault()
    return
  }

  productDragState.isDragging = true
  productDragState.draggedNode = node

  const dragData = {
    sourceType: 'product-internal',
    nodeId: node.id,
    nodeData: JSON.parse(JSON.stringify(node)),
  }

  if (event.dataTransfer) {
    event.dataTransfer.setData('text/plain', JSON.stringify(dragData))
    event.dataTransfer.effectAllowed = 'move'
  }

  const target = event.target as HTMLElement
  if (target) {
    target.style.opacity = '0.5'
  }
}

const handleProductTreeDragEnd = (event?: DragEvent) => {
  if (event?.target) {
    const target = event.target as HTMLElement
    target.style.opacity = ''
  }

  productDragState.isDragging = false
  productDragState.draggedNode = null
  productDragState.dragOverNode = null
  productDragState.canDrag = false
  productDragState.dropPosition = 'after'

  // 触发响应式更新 - 使用刷新方法代替直接修改
  nextTick(() => {
    refreshSystems()
  })
}

const handleProductTreeDragEnter = (event: DragEvent, node: TreeNode) => {
  event.preventDefault()

  // 只处理产品库内部拖拽和备选库到产品库的拖拽
  if (productDragState.isDragging || dragState.isDragging) {
    if (node.type === 'testCase' || node.type === 'testItem') {
      productDragState.dragOverNode = node
    }
  }
}

const handleProductTreeDragOver = (event: DragEvent, node: TreeNode) => {
  event.preventDefault()

  if (productDragState.isDragging || dragState.isDragging) {
    if (node.type === 'testCase' || node.type === 'testItem') {
      event.dataTransfer!.dropEffect = 'move'

      // 计算放置位置
      const rect = (event.target as HTMLElement).getBoundingClientRect()
      const y = event.clientY - rect.top
      const height = rect.height

      if (node.type === 'testCase') {
        // 用例节点：根据鼠标位置决定是放在前面还是后面
        productDragState.dropPosition = y < height / 2 ? 'before' : 'after'
      } else if (node.type === 'testItem') {
        // 测试项节点：放入其中
        productDragState.dropPosition = 'into'
      }
    }
  }
}

const handleProductTreeDragLeave = (event: DragEvent) => {
  event.preventDefault()
  // 只有当鼠标真正离开节点时才清除
  const relatedTarget = event.relatedTarget as HTMLElement
  if (!relatedTarget || !(event.target as HTMLElement).contains(relatedTarget)) {
    productDragState.dragOverNode = null
  }
}

const handleProductTreeDrop = (event: DragEvent, targetNode: TreeNode) => {
  event.preventDefault()

  try {
    const dragDataStr = event.dataTransfer?.getData('text/plain')
    if (!dragDataStr) return

    const dragData = JSON.parse(dragDataStr)

    if (dragData.sourceType === 'product-internal') {
      performProductInternalDrag(dragData, targetNode)
    } else if (dragData.sourceType === 'candidate') {
      performDragMigration(dragData, targetNode)
    }
  } catch (error) {
    ElMessage.error('拖拽操作失败')
  } finally {
    productDragState.dragOverNode = null
  }
}

// 执行产品库内部拖拽逻辑
const performProductInternalDrag = (dragData: any, targetNode: TreeNode) => {
  const sourceNode = dragData.nodeData

  if (!validateProductInternalDrag(sourceNode, targetNode)) {
    return
  }

  try {
    const sourceParent = findParentNode(productLibrary.value || [], sourceNode.id)
    if (!sourceParent || !sourceParent.children) {
      ElMessage.error('找不到源节点的父节点')
      return
    }

    const sourceIndex = sourceParent.children.findIndex(
      (child: TreeNode) => child.id === sourceNode.id,
    )
    if (sourceIndex === -1) {
      ElMessage.error('找不到源节点在父节点中的位置')
      return
    }

    // 计算目标位置
    const targetInfo = calculateTargetPosition(sourceNode, targetNode, sourceParent)
    if (!targetInfo) {
      ElMessage.error('无法计算目标位置')
      return
    }

    // 执行移动操作
    executeNodeMove(sourceNode, sourceIndex, sourceParent, targetInfo)

    // 重新排序相关节点（注意：此时sourceNode已经被移动到新位置）
    updateProductDragSequence(sourceNode, targetNode, sourceParent)

    // 自动选中移动后的节点
    selectedTestCase.value = sourceNode
    handleProductNodeClick(sourceNode)

    ElMessage.success(`已将 "${sourceNode.name}" 移动到新位置`)

    // 异步发送用例顺序更新请求
    updateCaseOrderAsync(sourceNode)
  } catch (error) {
    ElMessage.error('拖拽操作失败，请重试')
  }
}

// 验证产品库内部拖拽操作
const validateProductInternalDrag = (sourceNode: TreeNode, targetNode: TreeNode): boolean => {
  if (sourceNode.type !== 'testCase') {
    ElMessage.warning('只能拖拽用例节点')
    return false
  }

  if (targetNode.type !== 'testCase' && targetNode.type !== 'testItem') {
    ElMessage.warning('只能拖拽到用例节点或测试项节点')
    return false
  }

  if (sourceNode.id === targetNode.id) {
    return false
  }

  // 检查是否跨系统拖拽
  const sourceSystem = findSystemForNode(sourceNode)
  const targetSystem = findSystemForNode(targetNode)

  if (!sourceSystem || !targetSystem) {
    ElMessage.warning('无法确定用例所属系统')
    return false
  }

  if (sourceSystem.id !== targetSystem.id) {
    ElMessage.warning('用例只能在所属系统内拖动，不可跨系统移动')
    return false
  }

  return true
}

// 计算目标位置信息
const calculateTargetPosition = (
  sourceNode: TreeNode,
  targetNode: TreeNode,
  sourceParent: TreeNode,
) => {
  if (targetNode.type === 'testCase') {
    // 拖拽到用例节点
    const targetParent = findParentNode(productLibrary.value || [], targetNode.id)
    if (!targetParent || !targetParent.children) return null

    let targetIndex = targetParent.children.findIndex(
      (child: TreeNode) => child.id === targetNode.id,
    )

    // 如果源节点和目标节点在同一个父节点中，需要调整插入位置
    const isSameParent = sourceParent && sourceParent.id === targetParent.id
    if (isSameParent && sourceParent.children) {
      const sourceIndex = sourceParent.children.findIndex(
        (child: TreeNode) => child.id === sourceNode.id,
      )
      // 如果源节点在目标节点前面，目标索引需要减1
      if (sourceIndex < targetIndex) {
        targetIndex -= 1
      }
    }

    let insertIndex = targetIndex
    if (productDragState.dropPosition === 'before') {
      // 插入到目标用例前面
      insertIndex = targetIndex
    } else {
      // 插入到目标用例后面（默认）
      insertIndex = targetIndex + 1
    }

    return {
      targetParent,
      insertIndex,
      isSameParent,
    }
  } else if (targetNode.type === 'testItem') {
    // 拖拽到测试项节点：插入到该测试项的第一个位置
    if (!targetNode.children) {
      targetNode.children = []
    }

    return {
      targetParent: targetNode,
      insertIndex: 0,
      isSameParent: sourceParent && sourceParent.id === targetNode.id,
    }
  }

  return null
}

// 执行节点移动操作（简化版）
const executeNodeMove = (
  sourceNode: TreeNode,
  sourceIndex: number,
  sourceParent: TreeNode,
  targetInfo: any,
) => {
  const { targetParent, insertIndex, isSameParent } = targetInfo

  if (isSameParent && sourceParent.children) {
    // 同一个父节点内移动：直接调整位置
    const [movedNode] = sourceParent.children.splice(sourceIndex, 1)
    sourceParent.children.splice(insertIndex, 0, movedNode)
  } else {
    // 不同父节点间移动：先移除再插入
    if (sourceParent.children) {
      const [movedNode] = sourceParent.children.splice(sourceIndex, 1)
      if (!targetParent.children) {
        targetParent.children = []
      }
      targetParent.children.splice(insertIndex, 0, movedNode)
    }
  }
}

// 更新产品库拖拽后的序号（简化版）
const updateProductDragSequence = (
  movedNode: TreeNode,
  targetNode: TreeNode,
  originalSourceParent: TreeNode,
) => {
  // 重新查找移动后的节点所在的父节点
  const currentParent = findParentNode(productLibrary.value || [], movedNode.id)

  // 收集需要重排序的父节点
  const parentsToReorder = new Set<TreeNode>()

  // 如果是跨级移动，需要重排序原始源父节点
  if (
    originalSourceParent &&
    originalSourceParent !== currentParent &&
    originalSourceParent.children &&
    originalSourceParent.children.length > 0
  ) {
    parentsToReorder.add(originalSourceParent)
  }

  // 总是重排序当前父节点（移动后的位置）
  if (currentParent && currentParent.children && currentParent.children.length > 0) {
    parentsToReorder.add(currentParent)
  }

  // 对每个父节点进行重排序
  parentsToReorder.forEach((parent: TreeNode) => {
    // 直接重排序，强制刷新所有序号
    if (parent.children) {
      reorderSiblings(parent.children, 'testCase')

      // 同步更新用例详情
      parent.children.forEach((child: TreeNode) => {
        if (child.type === 'testCase' && child.usecaseDetail) {
          child.usecaseDetail.testCaseName = child.name
          child.usecaseDetail.identifier = generateIdentifier(child)
        }
      })
    }
  })

  // 强制触发Vue响应式更新 - 使用刷新方法
  refreshSystems()

  // 强制更新界面
  nextTick(() => {
    if (movedNode.usecaseDetail) {
      const parsed = parseTestCaseName(movedNode.name)
      usecaseDetailForm.caseName = parsed.customName
    }

    // 强制刷新树形组件
    if (productTreeRef.value) {
      productTreeRef.value.filter('')
    }

    // 再次强制更新
    setTimeout(() => {
      // 使用刷新方法更新数据
      refreshSystems()
    }, 50)
  })
}

// 监听窗口大小变化 - 将在主onMounted中处理

// 生成产品库用例标识
const generateProductIdentifier = (testCaseNode: TreeNode): string => {
  if (!testCaseNode) return ''

  // 查找父级节点
  const findParentNodes = (
    node: TreeNode,
    tree: TreeNode[],
    parents: TreeNode[] = [],
  ): TreeNode[] => {
    for (const item of tree) {
      if (item.id === node.id) {
        return parents
      }
      if (item.children) {
        const result = findParentNodes(node, item.children, [...parents, item])
        if (result.length > 0 || result === parents) {
          return result.length > 0 ? result : [...parents, item]
        }
      }
    }
    return []
  }

  const parentNodes = findParentNodes(testCaseNode, productLibrary.value || [])

  if (parentNodes.length < 3) return '' // 需要至少3级父节点：系统、功能、测试项

  const systemNode = parentNodes[0] // 系统
  const functionNode = parentNodes[1] // 功能
  const testItemNode = parentNodes[2] // 测试项

  // 生成系统名称首字母缩写
  const systemAbbr = getSystemAbbreviation(systemNode.name)

  // 生成功能编号 (GN + 序号)
  const functionIndex =
    (systemNode.children?.findIndex((child: TreeNode) => child.id === functionNode.id) ?? -1) + 1 ||
    1
  const functionCode = `GN${functionIndex.toString().padStart(2, '0')}`

  // 生成测试项编号
  const testItemIndex =
    (functionNode.children?.findIndex((child: TreeNode) => child.id === testItemNode.id) ?? -1) +
      1 || 1
  const testItemCode = testItemIndex.toString().padStart(3, '0')

  // 生成用例编号
  const testCaseIndex =
    (testItemNode.children?.findIndex((child: TreeNode) => child.id === testCaseNode.id) ?? -1) +
      1 || 1
  const testCaseCode = testCaseIndex.toString().padStart(3, '0')

  return `${systemAbbr}-${functionCode}-${testItemCode}-${testCaseCode}`
}

const generateCandidateIdentifier = (testCaseNode: TreeNode): string => {
  if (!testCaseNode) return ''

  // 查找父级节点
  const findParentNodes = (
    node: TreeNode,
    tree: TreeNode[],
    parents: TreeNode[] = [],
  ): TreeNode[] => {
    for (const item of tree) {
      if (item.id === node.id) {
        return parents
      }
      if (item.children) {
        const result = findParentNodes(node, item.children, [...parents, item])
        if (result.length > 0 || result === parents) {
          return result.length > 0 ? result : [...parents, item]
        }
      }
    }
    return []
  }

  const parentNodes = findParentNodes(testCaseNode, candidateLibrary.value || [])

  if (parentNodes.length < 3) return '' // 需要至少3级父节点：系统、功能、测试项

  const systemNode = parentNodes[0] // 系统
  const functionNode = parentNodes[1] // 功能
  const testItemNode = parentNodes[2] // 测试项

  // 生成系统名称首字母缩写
  const systemAbbr = getSystemAbbreviation(systemNode.name)

  // 生成功能编号 (GN + 序号)
  const functionIndex =
    (systemNode.children?.findIndex((child: TreeNode) => child.id === functionNode.id) ?? -1) + 1 ||
    1
  const functionCode = `GN${functionIndex.toString().padStart(2, '0')}`

  // 生成测试项编号
  const testItemIndex =
    (functionNode.children?.findIndex((child: TreeNode) => child.id === testItemNode.id) ?? -1) +
      1 || 1
  const testItemCode = testItemIndex.toString().padStart(3, '0')

  // 生成用例编号
  const testCaseIndex =
    (testItemNode.children?.findIndex((child: TreeNode) => child.id === testCaseNode.id) ?? -1) +
      1 || 1
  const testCaseCode = testCaseIndex.toString().padStart(3, '0')

  return `${systemAbbr}-${functionCode}-${testItemCode}-${testCaseCode}`
}

const loadCandidateUsecaseDetail = async (data: any) => {
  console.log('🔍 loadCandidateUsecaseDetail 被调用:', {
    testCaseName: data.name,
    testCaseId: data.id,
    tableName: data.tableName,
    hasTableName: !!data.tableName,
    hasId: !!data.id
  })

  // 尝试从API获取用例详情
  if (data.tableName && data.id) {
    try {
      console.log('📡 备选库发起API请求获取用例详情:', {
        tableName: data.tableName,
        id: data.id.toString()
      })

      // 直接调用API，不使用产品库的fetchCaseDetail函数
      const response = await apiService.caseManagement.getCaseDetail(data.tableName, data.id.toString())

      // 如果API调用成功，检查用例详情数据
      if (response && response.data) {
        // 使用正确的属性名称映射API响应到candidateDetailForm
        const caseDetail = response.data as any
        candidateDetailForm.testCaseName = caseDetail.caseName || ''
        candidateDetailForm.traceRelation = caseDetail.traceability || ''
        candidateDetailForm.testCaseSummary = caseDetail.summary || ''
        candidateDetailForm.usecaseInitialization = caseDetail.initialization || ''
        candidateDetailForm.prerequisitesConstraints = caseDetail.prerequisites || ''
        candidateDetailForm.testMethod = caseDetail.testMethod || ''
        candidateDetailForm.testCaseTerminationCondition = caseDetail.terminationCriteria || ''
        candidateDetailForm.testCasePassCriteria = caseDetail.acceptanceCriteria || ''
        candidateDetailForm.executionStatus = caseDetail.executionStatus || ''
        candidateDetailForm.executionResult = caseDetail.executionResult || ''
        candidateDetailForm.designer = caseDetail.designer || ''
        candidateDetailForm.reviewer = caseDetail.reviewer || ''
        candidateDetailForm.testTime = caseDetail.testTime || ''
        // 自动生成备选库用例标识，不从API响应中获取
        candidateDetailForm.identifier = data ? generateCandidateIdentifier(data) : ''

        // 处理测试步骤 - 适配API响应格式
        if (caseDetail.testSteps && Array.isArray(caseDetail.testSteps)) {
          candidateDetailForm.testSteps = caseDetail.testSteps.map((step: any, index: number) => ({
            id: step.id || Date.now() + index,
            sequence: step.stepNo || (index + 1).toString(),
            inputOperation: step.inputAction || '',
            expectedResult: step.expectedResult || '',
            actualResult: step.actualResult || '',
            evaluationCriteria: step.evaluationCriteria || '',
            testConclusion: step.testConclusion || ''
          }))
        } else {
          candidateDetailForm.testSteps = [
            {
              id: 1,
              sequence: 1,
              inputOperation: '',
              expectedResult: '',
              actualResult: '',
              evaluationCriteria: '',
              testConclusion: '',
            }
          ]
        }

        console.log('✅ 备选库用例详情API调用成功，已更新表单数据:', {
          testCaseName: candidateDetailForm.testCaseName,
          testStepsCount: candidateDetailForm.testSteps.length,
          executionStatus: candidateDetailForm.executionStatus
        })
        candidateSelectedRowIndex.value = null

        // 保存备选库原始表单数据
        saveOriginalCandidateFormData()
        return
      } else {
        // response.data 为 null，说明用例详情为空
        console.warn('⚠️ 备选库用例详情信息为空，使用本地数据')
        ElMessage.warning('该用例的详情信息为空，将显示默认数据')
        // 继续使用本地数据作为后备方案
      }
    } catch (error) {
      console.warn('备选库从API获取用例详情失败，使用本地数据:', error)
      // 继续使用本地数据作为后备方案
    }
  } else {
    console.warn('⚠️ 备选库无法发起API请求，缺少必要参数:', {
      tableName: data.tableName,
      id: data.id,
      missingTableName: !data.tableName,
      missingId: !data.id
    })
  }

  // 后备方案：使用本地数据
  if (data.usecaseDetail) {
    // 加载现有的用例详情数据
    Object.assign(candidateDetailForm, data.usecaseDetail)

    // 确保测试步骤数据结构完整
    if (!candidateDetailForm.testSteps || candidateDetailForm.testSteps.length === 0) {
      candidateDetailForm.testSteps = [
        {
          id: 1,
          sequence: 1,
          inputOperation: '',
          expectedResult: '',
          actualResult: '',
          evaluationCriteria: '',
          testConclusion: '',
        }
      ]
    } else {
      // 验证现有数据结构，确保所有必要字段存在
      candidateDetailForm.testSteps = candidateDetailForm.testSteps.map((step, index) => ({
        id: step.id || Date.now() + index,
        sequence: step.sequence || index + 1,
        inputOperation: step.inputOperation || '',
        expectedResult: step.expectedResult || '',
        actualResult: step.actualResult || '',
        evaluationCriteria: step.evaluationCriteria || '',
        testConclusion: step.testConclusion || '',
      }))
    }
  } else {
    // 创建默认的用例详情数据
    Object.assign(candidateDetailForm, createDefaultCandidateUsecaseDetail(data))
  }

  candidateSelectedRowIndex.value = null

  // 保存备选库原始表单数据
  saveOriginalCandidateFormData()
}

// 创建默认的备选库用例详情数据
const createDefaultCandidateUsecaseDetail = (testCase: TreeNode): any => {
  return {
    testCaseName: testCase.name || '',
    identifier: generateCandidateIdentifier(testCase),
    traceRelation: '',
    testCaseSummary: '',
    usecaseInitialization: '',
    prerequisitesConstraints: '',
    testMethod: '',
    testSteps: [
      {
        id: 1,
        sequence: 1,
        inputOperation: '',
        expectedResult: '',
        actualResult: '',
        evaluationCriteria: '',
        testConclusion: '',
      },
    ],
    testCaseTerminationCondition: '',
    testCasePassCriteria: '',
    executionStatus: '',
    executionResult: '',
    issueIdentification: '',
    designer: '',
    reviewer: '',
    testTime: '',
  }
}

const handleCandidateTestCaseNameChange = () => {
  if (selectedCandidateTestCase.value && candidateDetailForm.testCaseName) {
    // 获取原始名称中的序号
    const originalName = selectedCandidateTestCase.value.name
    const parsed = parseTestCaseName(originalName)

    // 构建新名称：新的自定义名称-原序号
    const newName = `${candidateDetailForm.testCaseName}-${parsed.sequence}`

    // 更新左侧树形结构中的节点名称
    selectedCandidateTestCase.value.name = newName

    // 更新用例详情中的名称
    if (selectedCandidateTestCase.value.usecaseDetail) {
      selectedCandidateTestCase.value.usecaseDetail.testCaseName = newName
    }

    // 重新生成标识符
    candidateDetailForm.identifier = generateCandidateIdentifier(selectedCandidateTestCase.value)
  }
}

// 备选库测试步骤操作方法
const addCandidateTestStep = () => {
  const newStep = {
    id: Date.now(),
    sequence: 0,
    inputOperation: '',
    expectedResult: '',
    actualResult: '',
    evaluationCriteria: '',
    testConclusion: '',
  }

  if (candidateSelectedRowIndex.value !== null) {
    candidateDetailForm.testSteps.splice(candidateSelectedRowIndex.value + 1, 0, newStep)
    candidateSelectedRowIndex.value = candidateSelectedRowIndex.value + 1
  } else {
    candidateDetailForm.testSteps.push(newStep)
    candidateSelectedRowIndex.value = candidateDetailForm.testSteps.length - 1
  }

  updateCandidateTestStepSequence()
}

const removeCandidateTestStep = () => {
  if (candidateSelectedRowIndex.value !== null && candidateDetailForm.testSteps.length > 1) {
    candidateDetailForm.testSteps.splice(candidateSelectedRowIndex.value, 1)

    if (candidateSelectedRowIndex.value >= candidateDetailForm.testSteps.length) {
      candidateSelectedRowIndex.value = candidateDetailForm.testSteps.length - 1
    }

    updateCandidateTestStepSequence()
  }
}

const updateCandidateTestStepSequence = () => {
  candidateDetailForm.testSteps.forEach((step, index) => {
    step.sequence = index + 1
  })
}

const handleCandidateRadioClick = (index: number) => {
  if (candidateSelectedRowIndex.value === index) {
    candidateSelectedRowIndex.value = null
  }
}

// 旧的版本管理函数已删除，现在使用新的版本管理系统

// 生成带序号后缀的用例名称
const generateTestCaseNameWithSequence = (baseName: string, testCaseNode: TreeNode): string => {
  if (!testCaseNode || !baseName) return baseName

  // 查找测试用例所在的测试项
  const findTestItemParent = (nodes: TreeNode[], targetId: string | number): TreeNode | null => {
    for (const node of nodes) {
      if (node.children) {
        for (const child of node.children) {
          if (child.id === targetId && child.type === 'testCase') {
            return node.type === 'testItem' ? node : null
          }
          if (child.children) {
            const result = findTestItemParent([child], targetId)
            if (result) return result
          }
        }
      }
    }
    return null
  }

  const testItemParent = findTestItemParent(productLibrary.value || [], testCaseNode.id)
  if (!testItemParent) return baseName

  // 获取同一测试项下的所有测试用例
  const testCasesInSameItem =
    testItemParent.children?.filter((child) => child.type === 'testCase') || []

  // 找到当前测试用例在列表中的位置（从1开始）
  const currentIndex = testCasesInSameItem.findIndex((tc) => tc.id === testCaseNode.id)
  if (currentIndex === -1) return baseName

  const sequenceNumber = currentIndex + 1

  // 如果名称已经包含序号后缀，则替换；否则添加
  const nameWithSequence = baseName.includes('-')
    ? `${baseName.split('-')[0]}-${sequenceNumber}`
    : `${baseName}-${sequenceNumber}`

  return nameWithSequence
}

// 更新树形目录中节点的名称
const updateTreeNodeName = (
  treeNode: TreeNode,
  newName: string,
  shouldAddSequence: boolean = false,
): string => {
  if (!treeNode || !newName) return ''

  // 如果需要添加序号后缀，则生成带序号的名称
  const finalName = shouldAddSequence
    ? generateTestCaseNameWithSequence(newName, treeNode)
    : newName

  // 递归查找并更新产品树中的节点
  const updateNodeInTree = (
    nodes: TreeNode[],
    targetId: string | number,
    newName: string,
  ): boolean => {
    for (const node of nodes) {
      if (node.id === targetId) {
        node.name = newName
        // 同时更新节点的 label 属性（如果存在）
        if (node.label) {
          node.label = newName
        }
        return true
      }
      if (node.children && node.children.length > 0) {
        if (updateNodeInTree(node.children, targetId, newName)) {
          return true
        }
      }
    }
    return false
  }

  // 更新产品树中的节点名称
  updateNodeInTree(productLibrary.value || [], treeNode.id, finalName)

  // 同时更新选中的测试用例对象的名称
  if (selectedTestCase.value && selectedTestCase.value.id === treeNode.id) {
    selectedTestCase.value.name = finalName
  }

  return finalName
}

// 旧的版本管理函数已删除，现在使用新的版本管理系统

// 旧的删除版本函数已删除

// 产品库用例导航方法
const previousTestCase = () => {
  if (!selectedTestCase.value) return

  const allTestCases = getAllTestCases(productLibrary.value || [])
  const currentIndex = allTestCases.findIndex((tc) => tc.id === selectedTestCase.value?.id)

  if (currentIndex > 0) {
    const previousTestCase = allTestCases[currentIndex - 1]
    handleProductNodeClick(previousTestCase)

    // 确保树形目录展开到显示选中的用例
    expandToShowNode(previousTestCase, productTreeRef.value)
  }
}

const nextTestCase = () => {
  if (!selectedTestCase.value) return

  const allTestCases = getAllTestCases(productLibrary.value || [])
  const currentIndex = allTestCases.findIndex((tc) => tc.id === selectedTestCase.value?.id)

  if (currentIndex < allTestCases.length - 1) {
    const nextTestCase = allTestCases[currentIndex + 1]
    handleProductNodeClick(nextTestCase)

    // 确保树形目录展开到显示选中的用例
    expandToShowNode(nextTestCase, productTreeRef.value)
  }
}

// 备选库用例导航方法
const previousCandidateTestCase = () => {
  if (!selectedCandidateTestCase.value) return

  const allTestCases = getAllTestCases(candidateLibrary.value || [])
  const currentIndex = allTestCases.findIndex((tc) => tc.id === selectedCandidateTestCase.value?.id)

  if (currentIndex > 0) {
    const previousTestCase = allTestCases[currentIndex - 1]
    handleCandidateNodeClick(previousTestCase)

    // 确保树形目录展开到显示选中的用例
    expandToShowNode(previousTestCase, candidateTreeRef.value)
  }
}

const nextCandidateTestCase = () => {
  if (!selectedCandidateTestCase.value) return

  const allTestCases = getAllTestCases(candidateLibrary.value || [])
  const currentIndex = allTestCases.findIndex((tc) => tc.id === selectedCandidateTestCase.value?.id)

  if (currentIndex < allTestCases.length - 1) {
    const nextTestCase = allTestCases[currentIndex + 1]
    handleCandidateNodeClick(nextTestCase)

    // 确保树形目录展开到显示选中的用例
    expandToShowNode(nextTestCase, candidateTreeRef.value)
  }
}

// 展开树形目录到显示指定节点的辅助方法
const expandToShowNode = (targetNode: TreeNode, treeRef: ElTreeInstance | null): void => {
  if (!treeRef || !targetNode) return

  // 使用 Element Plus Tree 的 setCurrentKey 方法设置当前选中节点
  treeRef.setCurrentKey(targetNode.id)

  // 确保父节点都展开
  const expandParentNodes = (nodeId: string | number) => {
    const node = treeRef.getNode(nodeId)
    if (node && node.parent && node.parent.key !== undefined) {
      node.parent.expanded = true
      expandParentNodes(node.parent.key)
    }
  }

  expandParentNodes(targetNode.id)
}

// 旧的版本切换兼容函数已删除






















// 日期格式化函数
const formatDateToYMD = (date: Date | string | null): string => {
  if (!date) return ''

  try {
    const d = typeof date === 'string' ? new Date(date) : date
    if (isNaN(d.getTime())) return ''

    const year = d.getFullYear()
    const month = String(d.getMonth() + 1).padStart(2, '0')
    const day = String(d.getDate()).padStart(2, '0')

    return `${year}.${month}.${day}`
  } catch (error) {
    console.error('日期格式化错误:', error)
    return ''
  }
}

// 解析日期字符串（支持多种格式）
const parseDateString = (dateStr: string): Date | null => {
  if (!dateStr) return null

  try {
    // 支持 YYYY.MM.DD 格式
    if (/^\d{4}\.\d{2}\.\d{2}$/.test(dateStr)) {
      const [year, month, day] = dateStr.split('.').map(Number)
      return new Date(year, month - 1, day)
    }

    // 支持其他标准格式
    const date = new Date(dateStr)
    return isNaN(date.getTime()) ? null : date
  } catch (error) {
    console.error('日期解析错误:', error)
    return null
  }
}

// 产品库测试时间变化处理
const handleProductTestDateChange = (value: string) => {
  if (value) {
    usecaseDetailForm.testTime = formatDateToYMD(new Date(value))
  } else {
    usecaseDetailForm.testTime = ''
  }
}

// 备选库测试时间变化处理
const handleCandidateTestDateChange = (value: string) => {
  if (value) {
    candidateDetailForm.testTime = formatDateToYMD(new Date(value))
  } else {
    candidateDetailForm.testTime = ''
  }
}

// 浏览器连接方法
const connectBrowser = async () => {
  connectingBrowser.value = true

  try {
    // 检查认证状态
    const token = getAccessToken()
    if (!token) {
      ElMessage.error('未找到认证信息，请先登录')
      return
    }

    // 浏览器连接参数
    const connectParams = {
      remote_host: '*************',
      remote_port: 9222,
    }

    const response = await apiService.browser.connect(connectParams)
    console.log('🔗 浏览器连接API响应:', response)
    console.log('🔍 响应类型:', typeof response)
    console.log('🔍 响应是否为null:', response === null)
    console.log('🔍 响应是否为undefined:', response === undefined)

    // 检查响应是否有效
    if (!response || typeof response !== 'object') {
      console.error('❌ API响应无效:', response)
      ElMessage.error('API响应格式错误，请检查网络连接和服务器状态')
      return
    }

    // 检查响应状态和连接状态
    if (response.status === 'success' && response.data?.connection_state === 'connected') {
      browserConnected.value = true

      // 显示连接成功信息
      const { data } = response
      console.log('📊 连接数据:', data)

      // 构建状态信息
      const connectionInfo = `${data.remote_host}:${data.remote_port}`
      const statsMsg = data.pool_stats && data.user_pages_count !== undefined
        ? `已连接到 ${connectionInfo} | 连接池: ${data.pool_stats.active_connections}/${data.pool_stats.total_connections} 活跃 | ${data.pool_stats.total_users} 用户 | ${data.user_pages_count} 页面`
        : `已连接到 ${connectionInfo}`

      console.log('✅ 浏览器连接成功，准备启动悬浮窗')

      // 检查认证状态后再打开录制悬浮窗
      const accessToken = getAccessToken()
      const refreshToken = getRefreshToken()
      const hasValidAuth = (authStore.isLoggedIn || userStore.isLoggedIn) && accessToken

      if (!hasValidAuth) {
        console.warn('⚠️ 认证信息无效，跳过悬浮窗启动')
        ElMessage.warning('浏览器连接成功，但未找到认证信息，请先登录后使用录制功能')
        ElMessage.success(`${response.message} - ${statsMsg}`)
        return
      }

      // 检查token是否即将过期，如果是则提前刷新
      if (accessToken && isAccessTokenExpiringSoon(accessToken) && refreshToken) {
        console.log('🔄 Token即将过期，提前刷新')
        await smartRefreshToken().catch(error => {
          console.warn('提前刷新token失败:', error)
        })
      }

      // 自动打开录制悬浮窗
      console.log('🚀 启动录制悬浮窗...')
      try {
        const windowOpened = await floatingWindowManager.openFloatingWindow({
          title: '录制控制台',
          width: 400,
          height: 80,
        })

        if (windowOpened) {
          floatingWindowOpen.value = true
          console.log('✅ 录制悬浮窗启动成功')
          ElMessage.success(`${response.message} - ${statsMsg}`)

          // 设置消息处理器
          setupFloatingWindowEvents()
        } else {
          console.warn('❌ 录制悬浮窗启动失败')
          ElMessage.warning(`${response.message} - ${statsMsg}，但悬浮窗启动失败（可能被浏览器阻止弹窗）`)
        }
      } catch (floatingWindowError: any) {
        console.error('❌ 录制悬浮窗启动异常:', floatingWindowError)
        ElMessage.warning(`${response.message} - ${statsMsg}，但悬浮窗启动失败: ${floatingWindowError?.message || '未知错误'}`)
      }
    } else {
      // 连接失败或状态不正确
      const errorMsg = response.data?.connection_state
        ? `浏览器连接状态异常: ${response.data.connection_state}`
        : (response.message || '浏览器连接失败')

      console.error('❌ 浏览器连接失败:', {
        status: response.status,
        connection_state: response.data?.connection_state,
        message: response.message
      })

      ElMessage.error(errorMsg)
    }
  } catch (error: any) {
    let errorMsg = error?.message || '浏览器连接失败'

    // 根据错误类型提供具体的错误信息
    if (error?.message?.includes('认证失败')) {
      errorMsg = '认证失败，请重新登录'
    } else if (error?.message?.includes('无法连接到服务器') || error?.name === 'TypeError') {
      errorMsg = '无法连接到后端服务，请确认后端服务器已启动'
    } else if (error?.message?.includes('NetworkError') || error?.message?.includes('fetch')) {
      errorMsg = '网络连接失败，请检查网络状态和服务器地址'
    }

    ElMessage.error(errorMsg)
  } finally {
    connectingBrowser.value = false
  }
}

const disconnectBrowser = async () => {
  connectingBrowser.value = true
  try {
    console.log('🔌 开始断开浏览器连接...')
    const response = await apiService.browser.disconnect()
    console.log('🔗 浏览器断开连接API响应:', response)
    console.log('🔍 响应类型:', typeof response)
    console.log('🔍 响应是否为null:', response === null)
    console.log('🔍 响应是否为undefined:', response === undefined)

    // 检查响应是否有效
    if (!response || typeof response !== 'object') {
      console.error('❌ API响应无效:', response)
      ElMessage.error('API响应格式错误，请检查网络连接和服务器状态')
      return
    }

    // 检查断开连接状态
    if (response.status === 'success' && response.data?.connection_state === 'disconnected') {
      browserConnected.value = false
      console.log('✅ 浏览器断开连接成功')

      // 关闭录制悬浮窗
      try {
        floatingWindowManager.closeFloatingWindow()
        floatingWindowOpen.value = false
        console.log('✅ 录制悬浮窗已关闭')
      } catch (windowError) {
        console.warn('⚠️ 关闭悬浮窗时出现问题:', windowError)
      }

      ElMessage.success(response.message || '浏览器断开连接成功')
    } else {
      // 断开连接失败或状态不正确
      const errorMsg = response.data?.connection_state
        ? `浏览器断开连接状态异常: ${response.data.connection_state}`
        : (response.message || '浏览器断开连接失败')

      console.error('❌ 浏览器断开连接失败:', {
        status: response.status,
        connection_state: response.data?.connection_state,
        message: response.message
      })

      ElMessage.error(errorMsg)
    }
  } catch (error: any) {
    console.error('❌ 浏览器断开连接异常:', error)
    const errorMsg = error?.message || '浏览器断开连接失败'
    ElMessage.error(errorMsg)
  } finally {
    connectingBrowser.value = false
  }
}





// 录制悬浮窗控制方法
const toggleRecordingWindow = async () => {
  if (floatingWindowOpen.value) {
    floatingWindowManager.closeFloatingWindow()
    floatingWindowOpen.value = false
    ElMessage.info('录制悬浮窗已隐藏')
  } else {
    // 检查认证状态
    const accessToken = getAccessToken()
    const hasValidAuth = (authStore.isLoggedIn || userStore.isLoggedIn) && accessToken

    if (!hasValidAuth) {
      ElMessage.warning('未找到认证信息，请先登录后使用录制功能')
      return
    }

    // 检查token状态，如果即将过期则提前刷新
    if (accessToken && isAccessTokenExpiringSoon(accessToken)) {
      smartRefreshToken().catch(error => {
        console.warn('录制前token刷新失败:', error)
      })
    }

    const windowOpened = await floatingWindowManager.openFloatingWindow({
      title: '录制控制台',
      width: 400,
      height: 80,
    })

    if (windowOpened) {
      floatingWindowOpen.value = true
      ElMessage.info('录制悬浮窗已显示')

      // 设置消息处理器
      setupFloatingWindowEvents()
    } else {
      ElMessage.warning('悬浮窗启动失败（可能被浏览器阻止弹窗）')
    }
  }
}

const focusRecordingWindow = () => {
  floatingWindowManager.focusFloatingWindow()
  ElMessage.info('悬浮窗已置顶')
}

const setupFloatingWindowEvents = () => {
  // 监听悬浮窗初始化完成事件
  floatingWindowManager.on('floatingWindowReady', (data: any) => {
    ElMessage.success('录制悬浮窗已就绪');
  });



  floatingWindowManager.on('startRecord', (data: any) => {
    // 悬浮窗已经调用了API，这里只需要同步状态
    recordingState.isRecording = true
    recordingState.isPaused = false
    recordingState.currentUsecaseName = '录制中...' // 用例名称将在停止录制时设置
    recordingState.startTime = new Date()
    // 不再重复调用API，避免状态冲突
  })

  floatingWindowManager.on('pauseRecord', () => {
    // 悬浮窗已经调用了API，这里只需要同步状态
    recordingState.isPaused = true
    // 不再重复调用API，避免状态冲突
  })

  floatingWindowManager.on('resumeRecord', () => {
    // 悬浮窗已经调用了API，这里只需要同步状态
    recordingState.isPaused = false
    // 不再重复调用API，避免状态冲突
  })

  floatingWindowManager.on('stopRecord', (data: any) => {
    // 悬浮窗执行乐观更新，这里立即同步状态
    recordingState.isRecording = false
    recordingState.isPaused = false
    recordingState.currentUsecaseName = ''
    recordingState.startTime = null
    recordingState.duration = 0

    // 只有当有文件路径时才显示成功消息（表示API已成功响应）
    // 乐观更新时 filePath 为 null，不显示消息，等待后续的 recordingStatus 事件
    if (data && data.filePath) {
      ElMessage.success(`录制已停止，文件保存至: ${data.filePath}`)
    }
  })

  floatingWindowManager.on('cancelRecord', () => {
    // 悬浮窗已经调用了API，这里只需要同步状态
    recordingState.isRecording = false
    recordingState.isPaused = false
    recordingState.currentUsecaseName = ''
    recordingState.startTime = null
    recordingState.duration = 0
    ElMessage.info('录制已取消')
  })

  floatingWindowManager.on('screenshot', () => {
    takeScreenshot()
  })

  floatingWindowManager.on('close', () => {
    closeRecordingWindow()
  })

  // 处理悬浮窗的录制状态消息
  floatingWindowManager.on('recordingStatus', (data: any) => {
    if (data?.type === 'success') {
      ElMessage.success(data.message)
    } else if (data?.type === 'error') {
      ElMessage.error(data.message)
      // 如果录制操作失败，重置相应状态
      // 注意：停止录制使用乐观更新，失败时不回滚UI状态
      if (data.action === 'start') {
        recordingState.isRecording = false
        recordingState.isPaused = false
      } else if (data.action === 'pause') {
        recordingState.isPaused = false
      } else if (data.action === 'resume') {
        recordingState.isPaused = true
      } else if (data.action === 'cancel') {
        // 取消录制失败时回滚状态
        recordingState.isRecording = false
        recordingState.isPaused = false
        recordingState.currentUsecaseName = ''
        recordingState.startTime = null
        recordingState.duration = 0
      }
      // 停止录制失败时不回滚状态（乐观更新）
      // else if (data.action === 'stop') { /* 不回滚状态 */ }
    } else if (data?.type === 'warning') {
      ElMessage.warning(data.message)
    } else if (data?.type === 'info') {
      ElMessage.info(data.message)
    }
  })

  // 处理悬浮窗的认证错误
  floatingWindowManager.on('authError', (data: any) => {
    const message = data?.message || '认证失败'

    // 显示用户友好的错误信息
    if (message.includes('未找到认证信息')) {
      ElMessage.warning('请先登录后再使用录制功能')
    } else if (message.includes('已过期')) {
      ElMessage.error('登录已过期，请重新登录')
    } else {
      ElMessage.error('认证失败：' + message)
    }

    // 关闭悬浮窗
    closeRecordingWindow()

    // 如果是token过期，清除认证状态
    if (message.includes('已过期')) {
      handleAuthenticationFailure()
    }
  })
}

const closeRecordingWindow = () => {
  // 清理消息处理器
  floatingWindowManager.off('startRecord')
  floatingWindowManager.off('pauseRecord')
  floatingWindowManager.off('resumeRecord')
  floatingWindowManager.off('stopRecord')
  floatingWindowManager.off('screenshot')
  floatingWindowManager.off('close')
  floatingWindowManager.off('recordingStatus')
  floatingWindowManager.off('authError')

  floatingWindowManager.closeFloatingWindow()
  floatingWindowOpen.value = false
}

// 移除重复的 focusRecordingWindow 函数定义

const takeScreenshot = async () => {
  try {
    const result = await apiService.recording.screenshot()
    if (result && result.success) {
      ElMessage.success(result.message || '截图成功')
    } else {
      const errorMsg = result?.message || '截图失败'
      ElMessage.error(errorMsg)
    }
  } catch (error: any) {
    const errorMsg = error.message || '网络错误'
    ElMessage.error('截图失败: ' + errorMsg)
  }
}

// 截图展示相关方法
const handleProductImageClick = (index: number) => {
  // 点击图片直接打开模态框预览
  const actualIndex = getActualScreenshotIndex(index, 'product')
  const clickedScreenshot = sortedProductScreenshots.value[index]
  screenshotState.productCurrentIndex = actualIndex // 原始数组索引，用于选择状态
  screenshotState.productDisplayIndex = index // 排序列表索引，用于导航
  screenshotState.productCurrentImage = clickedScreenshot.url // 显示用户实际点击的图片
  screenshotState.productImageScale = 1 // 重置缩放
  screenshotState.productModalVisible = true
}

const handleCandidateImageClick = (index: number) => {
  // 点击图片直接打开模态框预览
  const actualIndex = getActualScreenshotIndex(index, 'candidate')
  const clickedScreenshot = sortedCandidateScreenshots.value[index]
  screenshotState.candidateCurrentIndex = actualIndex // 原始数组索引，用于选择状态
  screenshotState.candidateDisplayIndex = index // 排序列表索引，用于导航
  screenshotState.candidateCurrentImage = clickedScreenshot.url // 显示用户实际点击的图片
  screenshotState.candidateImageScale = 1 // 重置缩放
  screenshotState.candidateModalVisible = true
}

// 获取实际的截图索引（因为排序后索引会变化）
const getActualScreenshotIndex = (displayIndex: number, type: 'product' | 'candidate'): number => {
  const sortedList =
    type === 'product' ? sortedProductScreenshots.value : sortedCandidateScreenshots.value
  const screenshot = sortedList[displayIndex]
  return screenshots.value.findIndex((item: any) => item.id === screenshot.id)
}

// 切换选中状态
const handleProductToggleSelect = (index: number) => {
  const actualIndex = getActualScreenshotIndex(index, 'product')
  const selectedIndex = screenshotState.productSelectedIndices.indexOf(actualIndex)

  if (selectedIndex > -1) {
    // 取消选中
    screenshotState.productSelectedIndices.splice(selectedIndex, 1)

    // 获取被移除项的顺序
    const removedOrder = screenshotState.productSelectionOrder.get(actualIndex)
    screenshotState.productSelectionOrder.delete(actualIndex)

    // 重新调整其他选中项的顺序
    if (removedOrder !== undefined) {
      screenshotState.productSelectionOrder.forEach((order, idx) => {
        if (order > removedOrder) {
          screenshotState.productSelectionOrder.set(idx, order - 1)
        }
      })
    }
  } else {
    // 选中 - 添加到选中列表并设置选择顺序
    screenshotState.productSelectedIndices.push(actualIndex)
    const nextOrder = Math.max(0, ...Array.from(screenshotState.productSelectionOrder.values())) + 1
    screenshotState.productSelectionOrder.set(actualIndex, nextOrder)
  }

  // 保存选中状态到本地存储
  saveProductScreenshotSelection()
}

const handleCandidateToggleSelect = (index: number) => {
  const actualIndex = getActualScreenshotIndex(index, 'candidate')
  const selectedIndex = screenshotState.candidateSelectedIndices.indexOf(actualIndex)

  if (selectedIndex > -1) {
    // 取消选中
    screenshotState.candidateSelectedIndices.splice(selectedIndex, 1)

    // 获取被移除项的顺序
    const removedOrder = screenshotState.candidateSelectionOrder.get(actualIndex)
    screenshotState.candidateSelectionOrder.delete(actualIndex)

    // 重新调整其他选中项的顺序
    if (removedOrder !== undefined) {
      screenshotState.candidateSelectionOrder.forEach((order, idx) => {
        if (order > removedOrder) {
          screenshotState.candidateSelectionOrder.set(idx, order - 1)
        }
      })
    }
  } else {
    // 选中 - 添加到选中列表并设置选择顺序
    screenshotState.candidateSelectedIndices.push(actualIndex)
    const nextOrder =
      Math.max(0, ...Array.from(screenshotState.candidateSelectionOrder.values())) + 1
    screenshotState.candidateSelectionOrder.set(actualIndex, nextOrder)
  }

  // 保存选中状态到本地存储
  saveCandidateScreenshotSelection()
}

// 拖拽相关方法 - 产品库
const handleProductDragStart = (index: number, event: DragEvent) => {
  screenshotState.productDraggedIndex = index
  if (event.dataTransfer) {
    event.dataTransfer.effectAllowed = 'move'
    event.dataTransfer.setData('text/plain', index.toString())
  }
}

const handleProductDragOver = (index: number, event: DragEvent) => {
  event.preventDefault()
  screenshotState.productDragOverIndex = index
  if (event.dataTransfer) {
    event.dataTransfer.dropEffect = 'move'
  }
}

const handleProductDrop = (index: number, event: DragEvent) => {
  event.preventDefault()
  const draggedIndex = screenshotState.productDraggedIndex

  if (draggedIndex !== -1 && draggedIndex !== index) {
    // 重新排列选中的截图
    const draggedActualIndex = getActualScreenshotIndex(draggedIndex, 'product')
    const targetActualIndex = getActualScreenshotIndex(index, 'product')

    // 更新选中索引数组中的顺序
    const draggedSelectedIndex = screenshotState.productSelectedIndices.indexOf(draggedActualIndex)
    const targetSelectedIndex = screenshotState.productSelectedIndices.indexOf(targetActualIndex)

    if (draggedSelectedIndex > -1 && targetSelectedIndex > -1) {
      screenshotState.productSelectedIndices.splice(draggedSelectedIndex, 1)
      screenshotState.productSelectedIndices.splice(targetSelectedIndex, 0, draggedActualIndex)
    }
  }

  screenshotState.productDragOverIndex = -1
}

const handleProductDragEnd = () => {
  screenshotState.productDraggedIndex = -1
  screenshotState.productDragOverIndex = -1
}

// 拖拽相关方法 - 备选库
const handleCandidateDragStart = (index: number, event: DragEvent) => {
  screenshotState.candidateDraggedIndex = index
  if (event.dataTransfer) {
    event.dataTransfer.effectAllowed = 'move'
    event.dataTransfer.setData('text/plain', index.toString())
  }
}

const handleCandidateDragOver = (index: number, event: DragEvent) => {
  event.preventDefault()
  screenshotState.candidateDragOverIndex = index
  if (event.dataTransfer) {
    event.dataTransfer.dropEffect = 'move'
  }
}

const handleCandidateDrop = (index: number, event: DragEvent) => {
  event.preventDefault()
  const draggedIndex = screenshotState.candidateDraggedIndex

  if (draggedIndex !== -1 && draggedIndex !== index) {
    // 重新排列选中的截图
    const draggedActualIndex = getActualScreenshotIndex(draggedIndex, 'candidate')
    const targetActualIndex = getActualScreenshotIndex(index, 'candidate')

    // 更新选中索引数组中的顺序
    const draggedSelectedIndex =
      screenshotState.candidateSelectedIndices.indexOf(draggedActualIndex)
    const targetSelectedIndex = screenshotState.candidateSelectedIndices.indexOf(targetActualIndex)

    if (draggedSelectedIndex > -1 && targetSelectedIndex > -1) {
      screenshotState.candidateSelectedIndices.splice(draggedSelectedIndex, 1)
      screenshotState.candidateSelectedIndices.splice(targetSelectedIndex, 0, draggedActualIndex)
    }
  }

  screenshotState.candidateDragOverIndex = -1
}

const handleCandidateDragEnd = () => {
  screenshotState.candidateDraggedIndex = -1
  screenshotState.candidateDragOverIndex = -1
}

const closeProductModal = () => {
  screenshotState.productModalVisible = false
  screenshotState.productCurrentImage = ''
  screenshotState.productImageScale = 1
  screenshotState.productDisplayIndex = 0
}

const closeCandidateModal = () => {
  screenshotState.candidateModalVisible = false
  screenshotState.candidateCurrentImage = ''
  screenshotState.candidateImageScale = 1
  screenshotState.candidateDisplayIndex = 0
}

// 产品库模态框导航功能
const previousProductImage = () => {
  if (screenshotState.productDisplayIndex > 0) {
    screenshotState.productDisplayIndex--
    const newScreenshot = sortedProductScreenshots.value[screenshotState.productDisplayIndex]
    screenshotState.productCurrentIndex = getActualScreenshotIndex(
      screenshotState.productDisplayIndex,
      'product',
    )
    screenshotState.productCurrentImage = newScreenshot.url
    screenshotState.productImageScale = 1 // 重置缩放
  }
}

const nextProductImage = () => {
  if (screenshotState.productDisplayIndex < sortedProductScreenshots.value.length - 1) {
    screenshotState.productDisplayIndex++
    const newScreenshot = sortedProductScreenshots.value[screenshotState.productDisplayIndex]
    screenshotState.productCurrentIndex = getActualScreenshotIndex(
      screenshotState.productDisplayIndex,
      'product',
    )
    screenshotState.productCurrentImage = newScreenshot.url
    screenshotState.productImageScale = 1 // 重置缩放
  }
}

// 备选库模态框导航功能
const previousCandidateImage = () => {
  if (screenshotState.candidateDisplayIndex > 0) {
    screenshotState.candidateDisplayIndex--
    const newScreenshot = sortedCandidateScreenshots.value[screenshotState.candidateDisplayIndex]
    screenshotState.candidateCurrentIndex = getActualScreenshotIndex(
      screenshotState.candidateDisplayIndex,
      'candidate',
    )
    screenshotState.candidateCurrentImage = newScreenshot.url
    screenshotState.candidateImageScale = 1 // 重置缩放
  }
}

const nextCandidateImage = () => {
  if (screenshotState.candidateDisplayIndex < sortedCandidateScreenshots.value.length - 1) {
    screenshotState.candidateDisplayIndex++
    const newScreenshot = sortedCandidateScreenshots.value[screenshotState.candidateDisplayIndex]
    screenshotState.candidateCurrentIndex = getActualScreenshotIndex(
      screenshotState.candidateDisplayIndex,
      'candidate',
    )
    screenshotState.candidateCurrentImage = newScreenshot.url
    screenshotState.candidateImageScale = 1 // 重置缩放
  }
}

// 产品库图片缩放功能
const zoomProductImage = (direction: 'in' | 'out') => {
  const step = 0.25
  if (direction === 'in' && screenshotState.productImageScale < 2) {
    screenshotState.productImageScale = Math.min(2, screenshotState.productImageScale + step)
  } else if (direction === 'out' && screenshotState.productImageScale > 0.5) {
    screenshotState.productImageScale = Math.max(0.5, screenshotState.productImageScale - step)
  }
}

const resetProductImageScale = () => {
  screenshotState.productImageScale = 1
}

const handleProductImageWheel = (event: WheelEvent) => {
  const delta = event.deltaY
  if (delta < 0) {
    zoomProductImage('in')
  } else {
    zoomProductImage('out')
  }
}

// 备选库图片缩放功能
const zoomCandidateImage = (direction: 'in' | 'out') => {
  const step = 0.25
  if (direction === 'in' && screenshotState.candidateImageScale < 2) {
    screenshotState.candidateImageScale = Math.min(2, screenshotState.candidateImageScale + step)
  } else if (direction === 'out' && screenshotState.candidateImageScale > 0.5) {
    screenshotState.candidateImageScale = Math.max(0.5, screenshotState.candidateImageScale - step)
  }
}

const resetCandidateImageScale = () => {
  screenshotState.candidateImageScale = 1
}

const handleCandidateImageWheel = (event: WheelEvent) => {
  const delta = event.deltaY
  if (delta < 0) {
    zoomCandidateImage('in')
  } else {
    zoomCandidateImage('out')
  }
}

// 产品库模态框选择功能
const isProductImageSelected = () => {
  return screenshotState.productSelectedIndices.includes(screenshotState.productCurrentIndex)
}

const toggleProductImageSelection = () => {
  const currentIndex = screenshotState.productCurrentIndex
  const selectedIndex = screenshotState.productSelectedIndices.indexOf(currentIndex)

  if (selectedIndex > -1) {
    // 取消选中
    screenshotState.productSelectedIndices.splice(selectedIndex, 1)

    // 获取被移除项的顺序
    const removedOrder = screenshotState.productSelectionOrder.get(currentIndex)
    screenshotState.productSelectionOrder.delete(currentIndex)

    // 重新调整其他选中项的顺序
    if (removedOrder !== undefined) {
      screenshotState.productSelectionOrder.forEach((order, idx) => {
        if (order > removedOrder) {
          screenshotState.productSelectionOrder.set(idx, order - 1)
        }
      })
    }
  } else {
    // 选中 - 添加到选中列表并设置选择顺序
    screenshotState.productSelectedIndices.push(currentIndex)
    const nextOrder = Math.max(0, ...Array.from(screenshotState.productSelectionOrder.values())) + 1
    screenshotState.productSelectionOrder.set(currentIndex, nextOrder)
  }

  // 保存选中状态到本地存储
  saveProductScreenshotSelection()
}

// 备选库模态框选择功能
const isCandidateImageSelected = () => {
  return screenshotState.candidateSelectedIndices.includes(screenshotState.candidateCurrentIndex)
}

const toggleCandidateImageSelection = () => {
  const currentIndex = screenshotState.candidateCurrentIndex
  const selectedIndex = screenshotState.candidateSelectedIndices.indexOf(currentIndex)

  if (selectedIndex > -1) {
    // 取消选中
    screenshotState.candidateSelectedIndices.splice(selectedIndex, 1)

    // 获取被移除项的顺序
    const removedOrder = screenshotState.candidateSelectionOrder.get(currentIndex)
    screenshotState.candidateSelectionOrder.delete(currentIndex)

    // 重新调整其他选中项的顺序
    if (removedOrder !== undefined) {
      screenshotState.candidateSelectionOrder.forEach((order, idx) => {
        if (order > removedOrder) {
          screenshotState.candidateSelectionOrder.set(idx, order - 1)
        }
      })
    }
  } else {
    // 选中 - 添加到选中列表并设置选择顺序
    screenshotState.candidateSelectedIndices.push(currentIndex)
    const nextOrder =
      Math.max(0, ...Array.from(screenshotState.candidateSelectionOrder.values())) + 1
    screenshotState.candidateSelectionOrder.set(currentIndex, nextOrder)
  }

  // 保存选中状态到本地存储
  saveCandidateScreenshotSelection()
}

// 全局键盘事件处理
const handleGlobalKeydown = (event: KeyboardEvent): void => {
  // 只在模态框打开时处理键盘事件
  if (screenshotState.productModalVisible) {
    switch (event.key) {
      case 'ArrowLeft':
        event.preventDefault()
        previousProductImage()
        break
      case 'ArrowRight':
        event.preventDefault()
        nextProductImage()
        break
      case 'Escape':
        event.preventDefault()
        closeProductModal()
        break
    }
  } else if (screenshotState.candidateModalVisible) {
    switch (event.key) {
      case 'ArrowLeft':
        event.preventDefault()
        previousCandidateImage()
        break
      case 'ArrowRight':
        event.preventDefault()
        nextCandidateImage()
        break
      case 'Escape':
        event.preventDefault()
        closeCandidateModal()
        break
    }
  }
}

// 截图选中状态的持久化存储
const saveProductScreenshotSelection = () => {
  const currentTestCase = getCurrentProductTestCase()
  if (!currentTestCase) return

  const selectionData = {
    selectedIndices: screenshotState.productSelectedIndices,
    selectionOrder: Array.from(screenshotState.productSelectionOrder.entries()),
  }

  localStorage.setItem(
    `product_screenshot_selection_${currentTestCase.id}`,
    JSON.stringify(selectionData),
  )
}

const saveCandidateScreenshotSelection = () => {
  const currentTestCase = getCurrentCandidateTestCase()
  if (!currentTestCase) return

  const selectionData = {
    selectedIndices: screenshotState.candidateSelectedIndices,
    selectionOrder: Array.from(screenshotState.candidateSelectionOrder.entries()),
  }

  localStorage.setItem(
    `candidate_screenshot_selection_${currentTestCase.id}`,
    JSON.stringify(selectionData),
  )
}

const loadProductScreenshotSelection = () => {
  const currentTestCase = getCurrentProductTestCase()
  if (!currentTestCase) return

  const saved = localStorage.getItem(`product_screenshot_selection_${currentTestCase.id}`)
  if (saved) {
    try {
      const selectionData = JSON.parse(saved)
      screenshotState.productSelectedIndices = selectionData.selectedIndices || []
      screenshotState.productSelectionOrder = new Map(selectionData.selectionOrder || [])
    } catch (error) {
      // 加载产品库截图选中状态失败
    }
  } else {
    // 清空选中状态
    screenshotState.productSelectedIndices = []
    screenshotState.productSelectionOrder.clear()
  }
}

const loadCandidateScreenshotSelection = () => {
  const currentTestCase = getCurrentCandidateTestCase()
  if (!currentTestCase) return

  const saved = localStorage.getItem(`candidate_screenshot_selection_${currentTestCase.id}`)
  if (saved) {
    try {
      const selectionData = JSON.parse(saved)
      screenshotState.candidateSelectedIndices = selectionData.selectedIndices || []
      screenshotState.candidateSelectionOrder = new Map(selectionData.selectionOrder || [])
    } catch (error) {
      // 加载备选库截图选中状态失败
    }
  } else {
    // 清空选中状态
    screenshotState.candidateSelectedIndices = []
    screenshotState.candidateSelectionOrder.clear()
  }
}

// 获取当前选中的测试用例
const getCurrentProductTestCase = () => {
  if (!selectedTestCase.value || selectedTestCase.value.type !== 'testCase') return null
  return selectedTestCase.value
}

const getCurrentCandidateTestCase = () => {
  if (!selectedCandidateTestCase.value || selectedCandidateTestCase.value.type !== 'testCase')
    return null
  return selectedCandidateTestCase.value
}

const startRecording = async (fromFloatingWindow = false) => {
  try {
    console.log('🎬 开始录制...')
    const result = await apiService.recording.start()
    console.log('🎬 开始录制API响应:', result)

    // 检查响应是否有效
    if (!result || typeof result !== 'object') {
      console.error('❌ 开始录制API响应无效:', result)
      if (!fromFloatingWindow) {
        ElMessage.error('API响应格式错误，请检查网络连接和服务器状态')
      }
      return
    }

    // 检查录制状态 - 根据实际API响应格式调整
    if (result.status === 'success') {
      console.log('✅ 开始录制成功')

      // 更新录制状态
      recordingState.isRecording = true
      recordingState.isPaused = false
      recordingState.currentUsecaseName = '录制中...'
      recordingState.startTime = new Date()
      recordingState.duration = 0

      if (!fromFloatingWindow) {
        ElMessage.success(result.message || '开始录制成功')
      }
    } else {
      const errorMsg = result?.message || '开始录制失败'
      console.error('❌ 开始录制失败:', {
        status: result.status,
        message: result.message,
        data: result.data
      })
      if (!fromFloatingWindow) {
        ElMessage.error(errorMsg)
      }
    }
  } catch (error: any) {
    console.error('❌ 开始录制异常:', error)

    // 处理API服务抛出的错误（通常包含后端的错误信息）
    let errorMsg = '网络错误'
    if (error.message) {
      // 如果错误信息是后端返回的具体错误，直接显示
      if (error.message.includes('录制已开始') ||
          error.message.includes('录制') ||
          error.message.includes('浏览器')) {
        errorMsg = error.message
      } else {
        errorMsg = '开始录制失败: ' + error.message
      }
    }

    if (!fromFloatingWindow) {
      ElMessage.error(errorMsg)
    }
  }
}

const pauseRecording = async (fromFloatingWindow = false) => {
  try {
    console.log('⏸️ 暂停录制...')
    const result = await apiService.recording.pause()
    console.log('⏸️ 暂停录制API响应:', result)

    // 检查响应是否有效
    if (!result || typeof result !== 'object') {
      console.error('❌ 暂停录制API响应无效:', result)
      if (!fromFloatingWindow) {
        ElMessage.error('API响应格式错误，请检查网络连接和服务器状态')
      }
      return
    }

    // 检查录制状态 - 根据实际API响应格式调整
    if (result.status === 'success') {
      console.log('✅ 暂停录制成功')

      // 更新录制状态
      recordingState.isPaused = true

      if (!fromFloatingWindow) {
        ElMessage.success(result.message || '暂停录制成功')
      }
    } else {
      const errorMsg = result?.message || '暂停录制失败'
      console.error('❌ 暂停录制失败:', {
        status: result.status,
        message: result.message,
        data: result.data
      })
      if (!fromFloatingWindow) {
        ElMessage.error(errorMsg)
      }
    }
  } catch (error: any) {
    console.error('❌ 暂停录制异常:', error)

    // 处理API服务抛出的错误（通常包含后端的错误信息）
    let errorMsg = '网络错误'
    if (error.message) {
      // 如果错误信息是后端返回的具体错误，直接显示
      if (error.message.includes('录制') ||
          error.message.includes('浏览器')) {
        errorMsg = error.message
      } else {
        errorMsg = '暂停录制失败: ' + error.message
      }
    }

    if (!fromFloatingWindow) {
      ElMessage.error(errorMsg)
    }
  }
}

const resumeRecording = async (fromFloatingWindow = false) => {
  try {
    console.log('▶️ 继续录制...')
    const result = await apiService.recording.resume()
    console.log('▶️ 继续录制API响应:', result)

    // 检查响应是否有效
    if (!result || typeof result !== 'object') {
      console.error('❌ 继续录制API响应无效:', result)
      if (!fromFloatingWindow) {
        ElMessage.error('API响应格式错误，请检查网络连接和服务器状态')
      }
      return
    }

    // 检查录制状态 - 根据实际API响应格式调整
    if (result.status === 'success') {
      console.log('✅ 继续录制成功')

      // 更新录制状态
      recordingState.isPaused = false

      if (!fromFloatingWindow) {
        ElMessage.success(result.message || '继续录制成功')
      }
    } else {
      const errorMsg = result?.message || '继续录制失败'
      console.error('❌ 继续录制失败:', {
        status: result.status,
        message: result.message,
        data: result.data
      })
      if (!fromFloatingWindow) {
        ElMessage.error(errorMsg)
      }
    }
  } catch (error: any) {
    console.error('❌ 继续录制异常:', error)

    // 处理API服务抛出的错误（通常包含后端的错误信息）
    let errorMsg = '网络错误'
    if (error.message) {
      // 如果错误信息是后端返回的具体错误，直接显示
      if (error.message.includes('录制') ||
          error.message.includes('浏览器')) {
        errorMsg = error.message
      } else {
        errorMsg = '继续录制失败: ' + error.message
      }
    }

    if (!fromFloatingWindow) {
      ElMessage.error(errorMsg)
    }
  }
}

const stopRecording = async (fromFloatingWindow = false, caseName?: string) => {
  // 如果是从悬浮窗调用，不执行乐观更新（悬浮窗已处理）
  if (!fromFloatingWindow) {
    // 乐观更新：立即更新UI状态
    console.log('🔄 主界面执行乐观更新 - 立即停止录制状态')
    recordingState.isRecording = false
    recordingState.isPaused = false
    recordingState.currentUsecaseName = ''
    recordingState.startTime = null
    recordingState.duration = 0

    // 显示正在处理的提示
    ElMessage.info('正在停止录制...')
  }

  try {
    console.log('⏹️ 停止录制...', caseName ? `用例名称: ${caseName}` : '')
    const result = await apiService.recording.stop(caseName)
    console.log('⏹️ 停止录制API响应:', result)

    // 检查响应是否有效
    if (!result || typeof result !== 'object') {
      console.error('❌ 停止录制API响应无效:', result)
      if (!fromFloatingWindow) {
        ElMessage.error('API响应格式错误，请检查网络连接和服务器状态')
      }
      return
    }

    // 检查录制状态 - 根据实际API响应格式调整
    if (result.status === 'success') {
      console.log('✅ 停止录制成功')

      // 如果是从悬浮窗调用，需要更新状态（悬浮窗已经乐观更新了）
      if (fromFloatingWindow) {
        recordingState.isRecording = false
        recordingState.isPaused = false
        recordingState.currentUsecaseName = ''
        recordingState.startTime = null
        recordingState.duration = 0
      }

      if (!fromFloatingWindow) {
        const successMsg = result.data?.file_path
          ? `录制已停止，文件保存至: ${result.data.file_path}`
          : (result.message || '停止录制成功')
        ElMessage.success(successMsg)
      }
    } else {
      const errorMsg = result?.message || '停止录制失败'
      console.error('❌ 停止录制失败:', {
        status: result.status,
        message: result.message,
        data: result.data
      })
      if (!fromFloatingWindow) {
        ElMessage.error(errorMsg)
        // 主界面停止录制失败时不回滚状态（乐观更新）
      }
    }
  } catch (error: any) {
    console.error('❌ 停止录制异常:', error)

    // 处理API服务抛出的错误（通常包含后端的错误信息）
    let errorMsg = '网络错误'
    if (error.message) {
      // 如果错误信息是后端返回的具体错误，直接显示
      if (error.message.includes('录制') ||
          error.message.includes('浏览器')) {
        errorMsg = error.message
      } else {
        errorMsg = '停止录制失败: ' + error.message
      }
    }

    if (!fromFloatingWindow) {
      ElMessage.error(errorMsg)
      // 主界面停止录制异常时不回滚状态（乐观更新）
    }
  }
}

const cancelRecording = async (fromFloatingWindow = false) => {
  try {
    console.log('❌ 取消录制...')
    const result = await apiService.recording.cancel()
    console.log('❌ 取消录制API响应:', result)

    // 检查响应是否有效
    if (!result || typeof result !== 'object') {
      console.error('❌ 取消录制API响应无效:', result)
      if (!fromFloatingWindow) {
        ElMessage.error('API响应格式错误，请检查网络连接和服务器状态')
      }
      return
    }

    // 检查录制状态 - 根据实际API响应格式调整
    if (result.status === 'success') {
      console.log('✅ 取消录制成功')

      // 重置录制状态
      recordingState.isRecording = false
      recordingState.isPaused = false
      recordingState.currentUsecaseName = ''
      recordingState.startTime = null
      recordingState.duration = 0

      if (!fromFloatingWindow) {
        ElMessage.success(result.message || '取消录制成功')
      }
    } else {
      const errorMsg = result?.message || '取消录制失败'
      console.error('❌ 取消录制失败:', {
        status: result.status,
        message: result.message,
        data: result.data
      })
      if (!fromFloatingWindow) {
        ElMessage.error(errorMsg)
      }
    }
  } catch (error: any) {
    console.error('❌ 取消录制异常:', error)

    // 处理API服务抛出的错误（通常包含后端的错误信息）
    let errorMsg = '网络错误'
    if (error.message) {
      // 如果错误信息是后端返回的具体错误，直接显示
      if (error.message.includes('录制') ||
          error.message.includes('浏览器')) {
        errorMsg = error.message
      } else {
        errorMsg = '取消录制失败: ' + error.message
      }
    }

    if (!fromFloatingWindow) {
      ElMessage.error(errorMsg)
    }
  }
}

const addAttachment = () => {
  attachmentDialogVisible.value = true
}

const handleFileChange = (file: any) => {
  newAttachment.file = file.raw
}

const confirmAddAttachment = () => {
  if (selectedTestCase.value && newAttachment.name) {
    if (!selectedTestCase.value.attachments) {
      selectedTestCase.value.attachments = []
    }
    selectedTestCase.value.attachments.push({
      id: Date.now(),
      name: newAttachment.name,
      url: '',
      type: newAttachment.file?.type || '',
      size: newAttachment.file?.size,
      file: newAttachment.file,
    })

    // 重置表单
    newAttachment.name = ''
    newAttachment.file = null
    fileList.value = []
    attachmentDialogVisible.value = false

    ElMessage.success('附件添加成功')
  }
}

const removeAttachment = (index: number) => {
  if (selectedTestCase.value && selectedTestCase.value.attachments) {
    selectedTestCase.value.attachments.splice(index, 1)
  }
}

const viewAttachment = (attachment: any) => {
  ElMessage.info(`查看附件: ${attachment.name}`)
}

// ============================================================================
// 用例详情相关方法
// ============================================================================

/**
 * 获取用例详情
 */
const fetchCaseDetail = async (tableId: string, indexId: string) => {
  if (!tableId || !indexId) {
    console.warn('获取用例详情：缺少必要参数', { tableId, indexId })
    return
  }

  try {
    isLoadingCaseDetail.value = true
    console.log('🔍 获取用例详情:', { tableId, indexId })

    const response = await apiService.caseManagement.getCaseDetail(tableId, indexId)

    console.log('📥 API响应数据:', response)

    // 根据实际API响应格式处理数据
    // response 已经是提取后的 data 部分，包含 caseInfoId, logId, versions 等
    if (response) {
      const caseData = response as any

      // 检查 caseInfoId 是否为 null 或不存在
      if (!caseData.caseInfoId) {
        console.warn('⚠️ 用例详情信息为空:', {
          _id: caseData._id,
          caseInfoId: caseData.caseInfoId,
          logId: caseData.logId,
          versions: caseData.versions
        })

        // 清空当前用例详情
        currentCaseDetail.value = null
        selectedCaseId.value = indexId
        selectedTableId.value = tableId

        // 清空表单数据
        usecaseDetailForm.caseName = ''
        usecaseDetailForm.traceability = ''
        usecaseDetailForm.summary = ''
        usecaseDetailForm.initialization = ''
        usecaseDetailForm.prerequisites = ''
        usecaseDetailForm.testMethod = ''
        usecaseDetailForm.terminationCriteria = ''
        usecaseDetailForm.acceptanceCriteria = ''
        usecaseDetailForm.executionStatus = ''
        usecaseDetailForm.executionResult = ''
        usecaseDetailForm.designer = ''
        usecaseDetailForm.reviewer = ''
        usecaseDetailForm.testTime = ''
        usecaseDetailForm.issueId = ''
        usecaseDetailForm.testSteps = []
        usecaseDetailForm.versions = []

        // 设置版本信息（可能为空）
        if (caseData.versions && caseData.versions.length > 0) {
          usecaseDetailForm.versions = caseData.versions
          selectedVersionIndex.value = caseData.versions.length - 1
        } else {
          usecaseDetailForm.versions = []
          selectedVersionIndex.value = 0
        }

        ElMessage.warning('该用例的详情信息为空，请检查数据完整性')
        return
      }

      // caseInfoId 存在，正常处理
      currentCaseDetail.value = caseData.caseInfoId
      selectedCaseId.value = indexId
      selectedTableId.value = tableId

      // 更新表单数据
      updateFormFromCaseDetail(caseData.caseInfoId)

      // 设置版本信息 - 从响应的versions获取
      if (caseData.versions && caseData.versions.length > 0) {
        usecaseDetailForm.versions = caseData.versions
        // 默认选择最新版本（数组最后一个）
        selectedVersionIndex.value = caseData.versions.length - 1
      } else {
        usecaseDetailForm.versions = []
        selectedVersionIndex.value = 0
      }

      // 处理操作记录
      if (caseData.logId && caseData.logId.operations) {
        console.log('📋 操作记录:', caseData.logId.operations)
        // 可以在这里处理操作记录的显示
      }

      console.log('✅ 用例详情获取成功:', {
        caseInfo: caseData.caseInfoId,
        versions: caseData.versions,
        logId: caseData.logId,
        caseName: caseData.caseInfoId?.caseName
      })
      ElMessage.success('用例详情加载成功')
    } else {
      console.error('❌ API响应为空:', response)
      throw new Error('API响应为空')
    }
  } catch (error: any) {
    console.error('❌ 获取用例详情失败:', error)
    ElMessage.error(error.message || '获取用例详情失败')
  } finally {
    isLoadingCaseDetail.value = false
  }
}

/**
 * 从用例详情数据更新表单
 */
const updateFormFromCaseDetail = (caseDetail: any) => {
  // 适配新的接口响应数据结构
  usecaseDetailForm.caseName = caseDetail.caseName || ''
  usecaseDetailForm.traceability = caseDetail.traceability || ''
  usecaseDetailForm.summary = caseDetail.summary || ''
  usecaseDetailForm.initialization = caseDetail.initialization || ''
  usecaseDetailForm.prerequisites = caseDetail.prerequisites || ''
  usecaseDetailForm.testMethod = caseDetail.testMethod || ''
  usecaseDetailForm.terminationCriteria = caseDetail.terminationCriteria || ''
  usecaseDetailForm.acceptanceCriteria = caseDetail.acceptanceCriteria || ''
  usecaseDetailForm.executionStatus = caseDetail.executionStatus || ''
  usecaseDetailForm.executionResult = caseDetail.executionResult || ''
  usecaseDetailForm.designer = caseDetail.designer || ''
  usecaseDetailForm.reviewer = caseDetail.reviewer || ''
  // 处理可能为 null 的 testTime
  usecaseDetailForm.testTime = caseDetail.testTime || ''
  // 使用接口返回的issueId，如果没有则自动生成
  usecaseDetailForm.issueId = caseDetail.issueId || (selectedTestCase.value ? generateProductIdentifier(selectedTestCase.value) : '')

  // 处理测试步骤 - 适配新的接口响应数据结构
  if (caseDetail.testSteps && Array.isArray(caseDetail.testSteps)) {
    usecaseDetailForm.testSteps = caseDetail.testSteps.map((step: any) => ({
      stepNo: step.stepNo || '',
      inputAction: step.inputAction || step.inputOperation || '',
      expectedResult: step.expectedResult || '',
      actualResult: step.actualResult || '',
      evaluationCriteria: step.evaluationCriteria || '',
      testConclusion: step.testConclusion || ''
    }))
  } else {
    usecaseDetailForm.testSteps = []
  }

  console.log('📝 表单数据已更新:', {
    caseName: usecaseDetailForm.caseName,
    testStepsCount: usecaseDetailForm.testSteps.length,
    executionStatus: usecaseDetailForm.executionStatus
  })

  // 保存原始表单数据
  saveOriginalFormData()
}

/**
 * 保存用例详情
 */
const saveCaseDetail = async () => {
  if (!selectedTableId.value) {
    ElMessage.warning('请先选择一个用例')
    return
  }

  // 数据验证
  if (!usecaseDetailForm.caseName.trim()) {
    ElMessage.warning('用例名称不能为空')
    return
  }

  // 验证测试步骤数据完整性
  const invalidSteps = usecaseDetailForm.testSteps.filter((step, index) => {
    if (!step.stepNo || !step.inputAction || !step.expectedResult) {
      console.warn(`测试步骤 ${index + 1} 数据不完整:`, step)
      return true
    }
    return false
  })

  if (invalidSteps.length > 0) {
    const confirmResult = await ElMessageBox.confirm(
      `检测到 ${invalidSteps.length} 个测试步骤数据不完整，是否继续保存？`,
      '数据验证',
      {
        confirmButtonText: '继续保存',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).catch(() => false)

    if (!confirmResult) {
      return
    }
  }

  try {
    isSavingCase.value = true
    console.log('💾 保存用例详情:', {
      tableId: selectedTableId.value,
      formData: usecaseDetailForm,
      originalData: currentCaseDetail.value
    })

    const saveData = {
      tableId: selectedTableId.value, // 使用 tableId 而不是 table_id
      caseData: {
        // 保留原始数据中的系统字段（如果存在）
        ...(currentCaseDetail.value && {
          _id: currentCaseDetail.value._id,
          savedBy: currentCaseDetail.value.savedBy,
          savedAt: currentCaseDetail.value.savedAt,
          sourceCaseLogId: currentCaseDetail.value.sourceCaseLogId,
          sourcePromptId: currentCaseDetail.value.sourcePromptId,
          generatedAt: currentCaseDetail.value.generatedAt,
          created_at: currentCaseDetail.value.created_at,
          updated_at: currentCaseDetail.value.updated_at
        }),

        // 基本信息字段
        caseName: usecaseDetailForm.caseName,
        traceability: usecaseDetailForm.traceability,
        summary: usecaseDetailForm.summary,
        initialization: usecaseDetailForm.initialization,
        prerequisites: usecaseDetailForm.prerequisites,
        testMethod: usecaseDetailForm.testMethod,
        terminationCriteria: usecaseDetailForm.terminationCriteria,
        acceptanceCriteria: usecaseDetailForm.acceptanceCriteria,

        // 执行信息字段
        executionStatus: usecaseDetailForm.executionStatus,
        executionResult: usecaseDetailForm.executionResult,
        designer: usecaseDetailForm.designer,
        reviewer: usecaseDetailForm.reviewer,
        testTime: usecaseDetailForm.testTime || null, // 保持与API一致，可能为null
        issueId: usecaseDetailForm.issueId,

        // 测试步骤
        testSteps: usecaseDetailForm.testSteps
      }
    }

    console.log('📤 发送保存请求:', JSON.stringify(saveData, null, 2))

    // 使用新的保存接口
    const response = await apiService.caseManagement.saveCaseDetail(
      selectedTableId.value,
      saveData.caseData
    )

    if (response) {
      console.log('✅ 用例保存成功:', response)
      ElMessage.success('用例保存成功')

      // 保存成功后立即重新获取用例详情以获取最新版本信息
      if (selectedCaseId.value && selectedTableId.value) {
        console.log('🔄 重新获取用例详情...')
        await fetchCaseDetail(selectedTableId.value, selectedCaseId.value)

        // 重新获取后，默认选择最新版本
        if (usecaseDetailForm.versions.length > 0) {
          selectedVersionIndex.value = usecaseDetailForm.versions.length - 1
        }
      }
    } else {
      throw new Error('保存响应异常')
    }
  } catch (error: any) {
    console.error('❌ 保存用例失败:', error)
    ElMessage.error(error.message || '保存用例失败')
  } finally {
    isSavingCase.value = false
  }
}

/**
 * 添加测试步骤
 */
const addTestStep = () => {
  const newStep = {
    stepNo: (usecaseDetailForm.testSteps.length + 1).toString(),
    inputAction: '',
    expectedResult: '',
    actualResult: '',
    evaluationCriteria: '',
    testConclusion: ''
  }
  usecaseDetailForm.testSteps.push(newStep)
}

/**
 * 删除测试步骤
 */
const removeTestStep = (index?: number) => {
  // 如果没有传入索引，删除最后一个步骤
  const targetIndex = index !== undefined ? index : usecaseDetailForm.testSteps.length - 1

  if (usecaseDetailForm.testSteps.length > 0 && targetIndex >= 0 && targetIndex < usecaseDetailForm.testSteps.length) {
    usecaseDetailForm.testSteps.splice(targetIndex, 1)
    // 重新编号
    usecaseDetailForm.testSteps.forEach((step, idx) => {
      step.stepNo = (idx + 1).toString()
    })
  }
}

/**
 * 格式化版本显示文本
 * 格式：名称（savedBy）-时间（savedAt精确到分钟）-VX.X（从V0.1开始递增）
 */
const formatVersionDisplayText = (version: any, index: number): string => {
  if (!version) {
    return `V0.${index + 1}`
  }

  // 处理savedBy，如果为空则使用默认值
  const savedBy = version.savedBy || 'system'

  // 处理savedAt时间格式，精确到分钟
  let timeStr = ''
  if (version.savedAt) {
    try {
      const date = new Date(version.savedAt)
      // 格式化为 YYYY-MM-DD HH:mm
      timeStr = date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      }).replace(/\//g, '-')
    } catch (error) {
      timeStr = version.savedAt
    }
  }

  // 生成版本号，从V0.1开始递增
  const versionNumber = `V0.${index + 1}`

  // 组合最终格式
  return `${savedBy}-${timeStr}-${versionNumber}`
}

/**
 * 获取版本显示文本数组
 */
const getVersionDisplayText = (versions: any[]) => {
  if (!versions || versions.length === 0) {
    return ['V0.1']
  }
  return versions.map((version, index) => formatVersionDisplayText(version, index))
}

/**
 * 获取当前选择的版本信息
 */
const getCurrentVersionInfo = () => {
  if (!usecaseDetailForm.versions || usecaseDetailForm.versions.length === 0) {
    return { index: 0, id: null, display: 'V0.1' }
  }

  const index = selectedVersionIndex.value
  const version = usecaseDetailForm.versions[index]
  const id = version?._id || null
  const display = formatVersionDisplayText(version, index)

  return { index, id, display }
}

/**
 * 检查表单是否被修改
 */
const checkFormModified = (): boolean => {
  if (!originalFormData.value) return false

  // 比较表单数据，确保字段与saveOriginalFormData保持一致
  const current = JSON.stringify({
    caseName: usecaseDetailForm.caseName,
    identifier: usecaseDetailForm.identifier,
    traceability: usecaseDetailForm.traceability,
    summary: usecaseDetailForm.summary,
    initialization: usecaseDetailForm.initialization,
    prerequisites: usecaseDetailForm.prerequisites,
    testMethod: usecaseDetailForm.testMethod,
    terminationCriteria: usecaseDetailForm.terminationCriteria,
    acceptanceCriteria: usecaseDetailForm.acceptanceCriteria,
    executionStatus: usecaseDetailForm.executionStatus,
    executionResult: usecaseDetailForm.executionResult,
    designer: usecaseDetailForm.designer,
    reviewer: usecaseDetailForm.reviewer,
    testTime: usecaseDetailForm.testTime,
    testSteps: usecaseDetailForm.testSteps
  })

  const original = JSON.stringify(originalFormData.value)
  return current !== original
}

/**
 * 保存原始表单数据（产品库）
 */
const saveOriginalFormData = () => {
  originalFormData.value = {
    caseName: usecaseDetailForm.caseName,
    identifier: usecaseDetailForm.identifier,
    traceability: usecaseDetailForm.traceability,
    summary: usecaseDetailForm.summary,
    initialization: usecaseDetailForm.initialization,
    prerequisites: usecaseDetailForm.prerequisites,
    testMethod: usecaseDetailForm.testMethod,
    terminationCriteria: usecaseDetailForm.terminationCriteria,
    acceptanceCriteria: usecaseDetailForm.acceptanceCriteria,
    executionStatus: usecaseDetailForm.executionStatus,
    executionResult: usecaseDetailForm.executionResult,
    designer: usecaseDetailForm.designer,
    reviewer: usecaseDetailForm.reviewer,
    testTime: usecaseDetailForm.testTime,
    testSteps: JSON.parse(JSON.stringify(usecaseDetailForm.testSteps))
  }
  isFormModified.value = false
}

/**
 * 检查备选库表单是否被修改
 */
const checkCandidateFormModified = (): boolean => {
  if (!originalCandidateFormData.value) return false

  // 比较备选库表单数据
  const current = JSON.stringify({
    testCaseName: candidateDetailForm.testCaseName,
    identifier: candidateDetailForm.identifier,
    traceRelation: candidateDetailForm.traceRelation,
    testCaseSummary: candidateDetailForm.testCaseSummary,
    usecaseInitialization: candidateDetailForm.usecaseInitialization,
    prerequisitesConstraints: candidateDetailForm.prerequisitesConstraints,
    testMethod: candidateDetailForm.testMethod,
    testCaseTerminationCondition: candidateDetailForm.testCaseTerminationCondition,
    testCasePassCriteria: candidateDetailForm.testCasePassCriteria,
    executionStatus: candidateDetailForm.executionStatus,
    executionResult: candidateDetailForm.executionResult,
    designer: candidateDetailForm.designer,
    reviewer: candidateDetailForm.reviewer,
    testTime: candidateDetailForm.testTime,
    testSteps: candidateDetailForm.testSteps
  })

  const original = JSON.stringify(originalCandidateFormData.value)
  return current !== original
}

/**
 * 保存备选库原始表单数据
 */
const saveOriginalCandidateFormData = () => {
  originalCandidateFormData.value = {
    testCaseName: candidateDetailForm.testCaseName,
    identifier: candidateDetailForm.identifier,
    traceRelation: candidateDetailForm.traceRelation,
    testCaseSummary: candidateDetailForm.testCaseSummary,
    usecaseInitialization: candidateDetailForm.usecaseInitialization,
    prerequisitesConstraints: candidateDetailForm.prerequisitesConstraints,
    testMethod: candidateDetailForm.testMethod,
    testCaseTerminationCondition: candidateDetailForm.testCaseTerminationCondition,
    testCasePassCriteria: candidateDetailForm.testCasePassCriteria,
    executionStatus: candidateDetailForm.executionStatus,
    executionResult: candidateDetailForm.executionResult,
    designer: candidateDetailForm.designer,
    reviewer: candidateDetailForm.reviewer,
    testTime: candidateDetailForm.testTime,
    testSteps: JSON.parse(JSON.stringify(candidateDetailForm.testSteps))
  }
  isCandidateFormModified.value = false
}

/**
 * 处理版本切换
 */
const handleVersionChange = async (versionIndex: number) => {
  // 检查当前表单是否有未保存的修改
  if (checkFormModified()) {
    try {
      await ElMessageBox.confirm(
        '当前用例有未保存的修改，切换版本将丢失这些修改。是否继续？',
        '提示',
        {
          confirmButtonText: '继续切换',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )
    } catch {
      // 用户取消，恢复原来的版本选择
      return
    }
  }

  selectedVersionIndex.value = versionIndex

  // 如果是最新版本（最后一个），直接使用当前的 currentCaseDetail
  if (versionIndex === usecaseDetailForm.versions.length - 1) {
    if (currentCaseDetail.value) {
      updateFormFromCaseDetail(currentCaseDetail.value)
      saveOriginalFormData()
      ElMessage.success('已切换到最新版本')
    }
    return
  }

  // 获取指定版本的用例详情
  const versionInfo = usecaseDetailForm.versions[versionIndex]
  if (!selectedTableId.value || !versionInfo || !versionInfo._id) {
    ElMessage.error('版本信息不完整，无法切换')
    return
  }

  try {
    isLoadingCaseDetail.value = true
    const response = await apiService.caseManagement.getCaseDetailByVersion(
      selectedTableId.value,
      versionInfo._id
    )

    console.log('📡 获取版本详情响应:', response)
    console.log('📡 响应类型:', typeof response)
    console.log('📡 响应字段:', response ? Object.keys(response) : 'null')

    // 检查多种可能的响应格式
    let caseData = null
    if (response && (response as any).status === 'success' && (response as any).data) {
      // 标准API响应格式
      caseData = (response as any).data
    } else if (response && response.data) {
      // 直接数据格式
      caseData = response.data
    } else if (response && (response as any)._id) {
      // 直接用例数据格式
      caseData = response
    }

    if (caseData) {
      updateFormFromCaseDetail(caseData)
      saveOriginalFormData()
      const versionDisplay = formatVersionDisplayText(versionInfo, versionIndex)
      ElMessage.success(`已切换到${versionDisplay}`)
    } else {
      console.error('❌ 版本数据格式错误，响应:', response)
      throw new Error('版本数据格式错误')
    }
  } catch (error: any) {
    console.error('❌ 获取版本详情失败:', error)
    ElMessage.error(`获取版本详情失败: ${error.message || '未知错误'}`)
    // 恢复原来的版本选择
    selectedVersionIndex.value = usecaseDetailForm.versions.length - 1
  } finally {
    isLoadingCaseDetail.value = false
  }
}

/**
 * 重置表单
 */
const resetForm = () => {
  if (currentCaseDetail.value) {
    updateFormFromCaseDetail(currentCaseDetail.value)
    ElMessage.success('表单已重置')
  } else {
    // 如果没有原始数据，清空表单
    usecaseDetailForm.caseName = ''
    usecaseDetailForm.traceability = ''
    usecaseDetailForm.summary = ''
    usecaseDetailForm.initialization = ''
    usecaseDetailForm.prerequisites = ''
    usecaseDetailForm.testMethod = ''
    usecaseDetailForm.terminationCriteria = ''
    usecaseDetailForm.acceptanceCriteria = ''
    usecaseDetailForm.executionStatus = ''
    usecaseDetailForm.executionResult = ''
    usecaseDetailForm.designer = ''
    usecaseDetailForm.reviewer = ''
    usecaseDetailForm.testTime = ''
    usecaseDetailForm.issueId = ''
    usecaseDetailForm.testSteps = []
    usecaseDetailForm.versions = []
    ElMessage.success('表单已清空')
  }
}




// 注意：开发环境检查已在上面定义为 isDevelopment

// 使用用例管理组合式函数
const {
  state,
  isLoading,
  hasError,
  isEmpty,
  productLibrary,
  candidateLibrary,
  statistics,
  fetchSystems,
  refreshSystems
} = useCaseManagement()

// 存储原始系统数据，用于拖动时构建请求体
const originalSystemsData = ref<any>(null)

// 使用浏览器连接管理
const {
  isConnected,
  connectionState,
  getBrowserStatus,
  checkAndRefreshStatus
} = useBrowserConnection()

// 默认用例路径选中状态管理
const selectedDefaultPath = ref<{tableName: string, testMethod: string} | null>(null)

// 内联编辑状态管理
const editingNodeId = ref<string | null>(null)
const editingNodeName = ref<string>('')
const editInputRef = ref<any>(null)
const editingParentNode = ref<TreeNode | null>(null) // 保存编辑节点的父节点信息
const isSaving = ref<boolean>(false) // 防重复提交标记

// 监听浏览器连接状态变化
watch(
  () => isConnected.value,
  (newValue, oldValue) => {
    console.log('🔄 浏览器连接状态变化:', {
      from: oldValue,
      to: newValue,
      connectionState: connectionState
    })
  },
  { immediate: true }
)

// 开始编辑节点名称
const startEditNodeName = (data: TreeNode, initialName: string = '', parentNode: TreeNode | null = null) => {
  editingNodeId.value = data.id.toString()
  editingNodeName.value = initialName
  editingParentNode.value = parentNode
  console.log('🖊️ 开始编辑节点:', {
    nodeId: data.id,
    nodeName: data.name,
    nodeType: data.type,
    initialName,
    parentNode: parentNode ? { id: parentNode.id, name: parentNode.name, type: parentNode.type } : null
  })
  nextTick(() => {
    if (editInputRef.value) {
      editInputRef.value.focus()
    }
  })
}

// 取消编辑
const cancelEdit = () => {
  editingNodeId.value = null
  editingNodeName.value = ''
  editingParentNode.value = null
}

// 保存节点名称
const saveNodeName = async (data: TreeNode) => {
  if (!editingNodeName.value.trim()) {
    ElMessage.warning('名称不能为空')
    return
  }

  // 防重复提交
  if (isSaving.value) {
    console.log('⚠️ 正在保存中，忽略重复请求')
    return
  }

  try {
    isSaving.value = true
    console.log('🚀 开始保存节点名称:', {
      nodeId: data.id,
      nodeType: data.type,
      newName: editingNodeName.value.trim(),
      isSaving: isSaving.value
    })
    // 根据节点类型调用不同的创建接口
    if (data.type === 'testItem') {
      console.log('💾 保存测试项节点:', {
        nodeId: data.id,
        nodeName: data.name,
        editingParentNode: editingParentNode.value ? {
          id: editingParentNode.value.id,
          name: editingParentNode.value.name,
          type: editingParentNode.value.type
        } : null
      })

      // 优先使用保存的父节点信息
      let systemNode: TreeNode | null = null
      let functionNode: TreeNode | null = null

      if (editingParentNode.value) {
        // 如果有保存的父节点信息，直接使用
        if (editingParentNode.value.type === 'function') {
          functionNode = editingParentNode.value
          // 查找系统节点（功能节点的父节点）
          const findSystemForFunction = (tree: TreeNode[]): TreeNode | null => {
            for (const item of tree) {
              if (item.type === 'system' && item.children) {
                for (const child of item.children) {
                  if (child.id === functionNode!.id) {
                    return item
                  }
                }
              }
            }
            return null
          }
          systemNode = findSystemForFunction(productLibrary.value || [])
        }
      }

      if (!systemNode || !functionNode) {
        ElMessage.error('无法获取父节点信息')
        console.error('❌ 父节点信息不完整:', { systemNode, functionNode })
        return
      }

      const testItemData = {
        systemName: systemNode.name,
        configItemName: functionNode.originalName || functionNode.name, // 使用原始配置项名称
        testItemName: editingNodeName.value.trim()
      }

      console.log('📡 发送创建测试项请求:', testItemData)

      const response = await apiService.caseManagement.createTestItem(testItemData)
      console.log('📡 创建测试项原始响应:', response)
      console.log('📡 响应类型:', typeof response)
      console.log('📡 响应字段:', response ? Object.keys(response) : 'null')
      console.log('📡 响应status字段:', (response as any)?.status)
      console.log('📡 响应message字段:', (response as any)?.message)
      console.log('📡 响应data字段:', (response as any)?.data)

      // 检查标准的API响应格式
      if (response && (response as any).status === 'success') {
        data.name = editingNodeName.value.trim()
        data.displayName = editingNodeName.value.trim()
        ElMessage.success((response as any).message || '测试项创建成功')
        console.log('✅ 测试项创建成功，响应数据:', response)
      } else {
        const errorMsg = (response as any)?.message || '测试项创建失败'
        ElMessage.error(errorMsg)
        console.error('❌ 测试项创建失败，响应数据:', response)
        console.error('❌ 响应status字段值:', (response as any)?.status)
        return
      }
    } else if (data.type === 'testCase') {
      // 获取父节点信息 - 使用递归查找
      const findParentNodes = (
        node: TreeNode,
        tree: TreeNode[],
        parents: TreeNode[] = [],
      ): TreeNode[] => {
        for (const item of tree) {
          if (item.id === node.id) {
            return parents
          }
          if (item.children) {
            const result = findParentNodes(node, item.children, [...parents, item])
            if (result.length > 0) {
              return result
            }
          }
        }
        return []
      }

      const parentNodes = findParentNodes(data, productLibrary.value || [])
      if (parentNodes.length < 2) {
        ElMessage.error('无法获取父节点信息')
        return
      }

      const systemNode = parentNodes[0] // 系统
      const functionNode = parentNodes[1] // 功能（配置项）
      const testItemNode = parentNodes[2] || functionNode // 测试项

      // 获取tableName（从functionNode中获取）
      const tableName = (functionNode as any).tableName || ''

      // 计算正确的index值（根据用例在测试项下的实际位置）
      let caseIndex = 0

      // 查找当前用例在测试项children中的位置
      if (testItemNode.children && testItemNode.children.length > 0) {
        const casePosition = testItemNode.children.findIndex(child => child.id === data.id)
        if (casePosition !== -1) {
          caseIndex = casePosition
        } else {
          // 如果没找到，说明是新添加的用例，使用children数组长度作为index
          caseIndex = testItemNode.children.length
        }
      }

      console.log('📍 计算用例index:', {
        testItemName: testItemNode.name,
        caseId: data.id,
        caseName: data.name,
        childrenCount: testItemNode.children?.length || 0,
        calculatedIndex: caseIndex,
        children: testItemNode.children?.map(child => ({ id: child.id, name: child.name })) || []
      })

      // 判断是产品库还是备选库（根据当前编辑的节点所在的库）
      // 检查节点是否在产品库中
      const isInProductLibrary = findNodeInTree(productLibrary.value || [], data.id)

      const caseData = {
        tableId: tableName,
        caseName: editingNodeName.value.trim(),
        systemName: systemNode.name,
        testMethodName: (testItemNode as any).originalName || testItemNode.name, // 使用原始测试项名称
        index: caseIndex, // 使用计算出的正确位置
        selected: isInProductLibrary // 产品库中的用例selected为true，备选库为false
      }

      console.log('📡 发送创建用例请求:', caseData)

      const response = await apiService.caseManagement.createCase(caseData)
      console.log('📡 创建用例响应:', response)
      console.log('📡 响应类型:', typeof response)
      console.log('📡 响应字段:', response ? Object.keys(response) : 'null')

      // 检查标准的API响应格式
      if (response && (response as any).status === 'success') {
        data.name = editingNodeName.value.trim()
        data.displayName = editingNodeName.value.trim()
        // 初始化用例详情表单
        if (data.usecaseDetail) {
          data.usecaseDetail.testCaseName = editingNodeName.value.trim()
        }
        ElMessage.success((response as any).message || '用例创建成功')
        console.log('✅ 用例创建成功')

        // 如果响应中包含用例ID，可以保存到节点中
        if ((response as any).data?.case_info_id) {
          console.log('📝 保存用例ID:', (response as any).data.case_info_id)
          // 可以将ID保存到节点的某个字段中，用于后续操作
        }
      } else {
        const errorMsg = (response as any)?.message || '用例创建失败'
        ElMessage.error(errorMsg)
        console.error('❌ 用例创建失败:', response)
        return
      }
    } else {
      // 其他类型直接更新名称
      data.name = editingNodeName.value.trim()
      data.displayName = editingNodeName.value.trim()
    }

    // 刷新数据
    await refreshSystems()

    // 结束编辑
    cancelEdit()
  } catch (error: any) {
    ElMessage.error('保存失败: ' + (error.message || '未知错误'))
    console.error('❌ 保存节点名称失败:', error)
  } finally {
    isSaving.value = false
    console.log('🔄 重置保存状态:', { isSaving: isSaving.value })
  }
}





// 监听内容变化，自动同步高度
watch(
  [usecaseDetailForm, candidateDetailForm, productLibrary, candidateLibrary],
  () => {
    // 延迟执行，确保DOM更新完成
    setTimeout(() => {
      syncLibrariesHeight()
    }, 100)
  },
  { deep: true },
)

onMounted(async () => {
  // 初始化认证stores
  authStore.init()

  // 检查浏览器连接状态
  console.log('🔍 页面加载时检查浏览器连接状态...')
  await checkAndRefreshStatus()
  console.log('🔍 当前浏览器连接状态:', {
    isConnected: isConnected.value,
    connectionState: connectionState
  })

  // 检查用户是否已登录
  if (!authStore.isLoggedIn && !userStore.isLoggedIn) {
    ElMessage.warning('请先登录后再使用此功能')
    router.push('/login')
    return
  }



  // 获取用例数据
  try {
    await fetchSystems()

    // 获取原始系统数据，用于拖动时构建请求体
    const systemsResponse = await apiService.caseManagement.getSystems()
    console.log('📡 获取原始系统数据响应:', systemsResponse)

    // 正确提取响应数据中的data部分
    if (systemsResponse && (systemsResponse as any).status === 'success' && (systemsResponse as any).data) {
      originalSystemsData.value = (systemsResponse as any).data
      console.log('✅ 保存原始系统数据:', originalSystemsData.value)
    } else if (systemsResponse && systemsResponse.data) {
      // 备用格式
      originalSystemsData.value = systemsResponse.data
      console.log('✅ 保存原始系统数据(备用格式):', originalSystemsData.value)
    } else {
      originalSystemsData.value = systemsResponse
      console.log('⚠️ 使用完整响应作为原始系统数据:', originalSystemsData.value)
    }

  } catch (error) {
    console.error('❌ 用例数据获取失败:', error)
    ElMessage.error('用例数据加载失败，请刷新页面重试')
  }

  // TODO: 临时注释 - 每分钟token刷新检查
  // 启动统一token管理器的自动刷新
  // tokenManager.startAutoRefresh()

  // 监听token管理器的认证失败事件
  tokenManager.on('authenticationFailed', (data: any) => {
    console.error('认证失败:', data.message)
    handleAuthenticationFailure(data.message)
  })

  // 暴露获取当前用户token的方法给悬浮窗使用
  ;(window as any).getCurrentUserToken = async () => {
    // 智能获取token，如果需要会自动刷新
    const tokenResult = await smartRefreshToken()
    return tokenResult.success ? (tokenResult.token || getAccessToken()) : null
  }

  // 暴露token管理器给悬浮窗使用
  ;(window as any).getTokenManager = () => {
    return {
      smartRefreshToken,
      getAccessToken,
      isTokenExpired: (token: string) => {
        try {
          const payload = JSON.parse(atob(token.split('.')[1]))
          return Date.now() >= payload.exp * 1000
        } catch {
          return true
        }
      }
    }
  }



  // 使用默认的树形结构（已登录用户可访问所有功能）

  // 初始化时选择第一个产品版本
  const productData = productLibrary.value || []
  if (
    productData.length > 0 &&
    productData[0].children &&
    productData[0].children.length > 0
  ) {
    selectedProduct.value = productData[0].children[0]
  }

  // 初始化截图选中状态
  loadProductScreenshotSelection()
  loadCandidateScreenshotSelection()

  // 设置高度监听器
  setupHeightWatchers()

  // 添加全局键盘事件监听
  document.addEventListener('keydown', handleGlobalKeydown)

  // 监听窗口大小变化
  window.addEventListener('resize', syncLibrariesHeight)

  // 设置ResizeObserver监听用例详情区域高度变化
  nextTick(() => {
    updateLibraryHeights()

    if (window.ResizeObserver) {
      try {
        resizeObserver.value = new ResizeObserver(() => {
          updateLibraryHeights()
        })

        // 延迟设置监听，确保DOM完全渲染
        setTimeout(() => {
          // 监听用例详情区域
          if (usecaseDetailRef.value && resizeObserver.value) {
            resizeObserver.value.observe(usecaseDetailRef.value)
          }
          if (candidateDetailRef.value && resizeObserver.value) {
            resizeObserver.value.observe(candidateDetailRef.value)
          }
        }, 100)
      } catch (error) {
        // ResizeObserver设置失败
      }
    } else {
      // 降级方案：定期检查高度变化
      setInterval(updateLibraryHeights, 1000)
    }
  })
})

// 刷新数据处理函数
const handleRefreshData = async () => {
  try {
    await refreshSystems()
    ElMessage.success('用例数据刷新成功')


  } catch (error) {
    console.error('❌ 用例数据刷新失败:', error)
    ElMessage.error('用例数据刷新失败，请检查网络连接')
  }
}



onUnmounted(() => {
  // 停止统一token管理器的自动刷新
  tokenManager.stopAutoRefresh()

  // 清理悬浮窗和事件监听器
  if (floatingWindowOpen.value) {
    closeRecordingWindow()
  }

  // 清理ResizeObserver
  if (resizeObserver.value) {
    resizeObserver.value.disconnect()
    resizeObserver.value = null
  }



  // 清理全局方法
  if (window.getCurrentUserToken) {
    delete window.getCurrentUserToken
  }

  // 移除全局键盘事件监听
  document.removeEventListener('keydown', handleGlobalKeydown)

  // 移除窗口大小变化监听
  window.removeEventListener('resize', syncLibrariesHeight)
})


</script>

<style scoped lang="scss">
@use '@/assets/styles/theme.scss' as theme;



.usecase-library-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh; // 改为最小高度，允许内容扩展
  width: 100%;
  max-width: 100vw; // 确保不超过视口宽度
  padding: 16px;
  background: theme.$bg-gradient-primary;
  box-sizing: border-box; // 包含padding在宽度计算中
  // 移除 overflow: hidden，允许内容自然扩展
}

.browser-connection-section {
  background: theme.$color-container;
  border: 1px solid theme.$color-border;
  border-radius: theme.$border-radius-medium;
  box-shadow: theme.$shadow-soft;
  padding: 16px;
  margin-bottom: 16px;

  .browser-controls {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;



    .browser-btn {
      transition: all theme.$transition-fast;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(theme.$color-primary, 0.3);
      }
    }

    .recording-btn {
      background-color: theme.$color-primary-accent;
      border-color: theme.$color-primary-accent;
      color: white;

      &:hover {
        background-color: theme.$color-primary-accent-darker;
        border-color: theme.$color-primary-accent-darker;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(theme.$color-primary-accent, 0.3);
      }
    }

    .focus-btn {
      background-color: #f39c12;
      border-color: #f39c12;
      color: white;

      &:hover {
        background-color: darken(#f39c12, 10%);
        border-color: darken(#f39c12, 10%);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(#f39c12, 0.3);
      }
    }

    .connection-status {
      display: flex;
      align-items: center;
      gap: 6px;
      color: theme.$color-primary-accent;
      font-weight: 500;
      font-size: 14px;

      .connected-icon {
        color: theme.$color-primary-accent;
        font-size: 16px;
      }
    }
  }
}

.main-content {
  flex: 1;
  min-height: calc(100vh - 120px); // 减去浏览器连接区域和padding的高度
  width: 100%;
  display: flex; // 确保主内容区域也是flex容器
  flex-direction: column; // 垂直布局
  // 移除 overflow: hidden，允许内容自然扩展
}

// 并排布局容器 - 优化水平布局
.libraries-container {
  // CSS变量定义，便于维护
  --library-gap: 16px;
  --library-min-height: calc(100vh - 120px);
  --section-header-height: 56px;
  --search-section-height: 72px;
  --version-section-height: 80px;
  --collapsed-width: 60px; // 折叠状态下的宽度

  display: grid;
  grid-template-columns: 1fr 1fr; // 使用Grid布局，两列等宽
  gap: var(--library-gap); // 两个库之间的间距
  height: auto; // 改为自动高度，允许内容扩展
  min-height: var(--library-min-height);
  width: 100%;
  align-items: start; // 顶部对齐

  // 当产品库折叠时
  &:has(.product-library-section.collapsed) {
    grid-template-columns: var(--collapsed-width) 1fr;
  }

  // 当备选库折叠时
  &:has(.candidate-library-section.collapsed) {
    grid-template-columns: 1fr var(--collapsed-width);
  }

  // 库区域基本样式
  > .product-library-section,
  > .candidate-library-section {
    min-width: 0; // 防止内容溢出
    transition: all 0.3s ease; // 折叠动画
    overflow: hidden; // 防止内容溢出
  }

  // 产品库始终在第一列
  > .product-library-section {
    grid-column: 1;
  }

  // 备选库始终在第二列
  > .candidate-library-section {
    grid-column: 2;
  }
}

.product-library-section {
  min-width: 0; // 防止内容溢出
  min-height: 600px; // 设置合理的最小高度
  height: auto; // 改为自动高度，允许内容扩展
  background: theme.$color-container;
  border: 1px solid theme.$color-border;
  border-radius: theme.$border-radius-medium;
  box-shadow: theme.$shadow-soft;
  display: flex;
  flex-direction: column;
  // 移除 overflow: hidden，允许内容根据需要扩展

  .section-header {
    padding: 16px;
    border-bottom: 1px solid theme.$color-border;
    background: linear-gradient(
      135deg,
      theme.$color-container 0%,
      theme.$color-container-lighter-2 100%
    );
    border-radius: theme.$border-radius-medium theme.$border-radius-medium 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 56px; // 确保标题栏高度一致

    h3 {
      margin: 0;
      font-size: 16px;
      color: theme.$color-text-primary;
      font-weight: 600;
      line-height: 1.4; // 确保文字垂直居中

      .case-count {
        font-size: 12px;
        font-weight: normal;
        color: theme.$color-success;
        margin-left: 8px;
        opacity: 0.8;
      }
    }

    .header-actions {
      display: flex;
      gap: 8px;

      .collapse-btn {
        width: 28px;
        height: 28px;
        background-color: rgba(theme.$color-primary, 0.1);
        border: 1px solid rgba(theme.$color-primary, 0.3);
        color: theme.$color-primary;
        transition: all 0.3s ease;

        &:hover {
          background-color: rgba(theme.$color-primary, 0.2);
          border-color: theme.$color-primary;
          transform: scale(1.1);
        }

        .el-icon {
          font-size: 14px;
        }
      }
    }
  }

  // 折叠状态下的窄条样式
  .collapsed-strip {
    width: 100%;
    height: 100%;
    background: linear-gradient(
      135deg,
      theme.$color-container 0%,
      theme.$color-container-lighter-2 100%
    );
    border: 1px solid theme.$color-border;
    border-radius: theme.$border-radius-medium;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    .expand-button {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 8px;
      padding: 16px 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      border-radius: theme.$border-radius-small;
      width: 100%;
      height: 120px;

      &:hover {
        background-color: rgba(theme.$color-primary, 0.1);
        transform: scale(1.05);
      }

      .expand-icon {
        font-size: 20px;
        color: theme.$color-primary;
        transition: all 0.3s ease;
      }

      .strip-label {
        font-size: 12px;
        color: theme.$color-text-secondary;
        writing-mode: vertical-rl; // 垂直文字
        text-orientation: mixed;
        font-weight: 500;
        letter-spacing: 2px;
      }

      &:hover .expand-icon {
        color: theme.$color-primary-accent;
        transform: scale(1.2);
      }

      &:hover .strip-label {
        color: theme.$color-text-primary;
      }
    }
  }

  .search-section {
    padding: 16px;
    border-bottom: 1px solid theme.$color-border;
    background: theme.$color-container-lighter-1;
    min-height: 72px; // 确保搜索区域高度一致

    .search-controls {
      display: flex;
      align-items: center;
      gap: 12px;
      height: 40px; // 固定控件高度
    }
  }

  .version-management-section {
    padding: 16px;
    border-bottom: 1px solid theme.$color-border;
    background: linear-gradient(
      135deg,
      theme.$color-container-lighter-2 0%,
      theme.$color-container-lighter-1 100%
    );
    min-height: 80px; // 确保版本管理区域高度一致

    .version-controls {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 20px;
      min-height: 48px; // 确保控件区域高度一致

      .version-actions {
        display: flex;
        align-items: center;
        gap: 8px;

        .version-btn {
          transition: all theme.$transition-fast;

          &.save-btn {
            background-color: theme.$color-primary-accent;
            border-color: theme.$color-primary-accent;
            color: white;

            &:hover {
              background-color: theme.$color-primary-accent-darker;
              border-color: theme.$color-primary-accent-darker;
              transform: translateY(-1px);
              box-shadow: 0 4px 8px rgba(theme.$color-primary-accent, 0.3);
            }
          }

          &.nav-btn {
            background-color: theme.$color-primary;
            border-color: theme.$color-primary;
            color: white;

            &:hover:not(:disabled) {
              background-color: theme.$color-primary-darker;
              border-color: theme.$color-primary-darker;
              transform: translateY(-1px);
              box-shadow: 0 4px 8px rgba(theme.$color-primary, 0.3);
            }

            &:disabled {
              opacity: 0.5;
              cursor: not-allowed;
            }
          }

          &.import-btn {
            background-color: #67c23a;
            border-color: #67c23a;
            color: white;

            &:hover:not(:disabled) {
              background-color: darken(#67c23a, 10%);
              border-color: darken(#67c23a, 10%);
              transform: translateY(-1px);
              box-shadow: 0 4px 8px rgba(#67c23a, 0.3);
            }
          }

          &.export-btn {
            background-color: #e6a23c;
            border-color: #e6a23c;
            color: white;

            &:hover:not(:disabled) {
              background-color: darken(#e6a23c, 10%);
              border-color: darken(#e6a23c, 10%);
              transform: translateY(-1px);
              box-shadow: 0 4px 8px rgba(#e6a23c, 0.3);
            }
          }

          &.reset-btn {
            background-color: #909399;
            border-color: #909399;
            color: white;

            &:hover:not(:disabled) {
              background-color: darken(#909399, 10%);
              border-color: darken(#909399, 10%);
              transform: translateY(-1px);
              box-shadow: 0 4px 8px rgba(#909399, 0.3);
            }
          }
        }
      }



      .version-selector {
        display: flex;
        align-items: center;
        gap: 12px;

        .version-label {
          font-size: 14px;
          color: theme.$color-text-primary;
          font-weight: 500;
          white-space: nowrap;
        }

        .version-select {
          min-width: 200px;
          max-width: 300px;

          :deep(.el-select__wrapper) {
            border-color: theme.$color-border;
            border-radius: theme.$border-radius-small;
            transition: all theme.$transition-fast;

            &:hover {
              border-color: theme.$color-primary;
            }

            &.is-focused {
              border-color: theme.$color-primary;
              box-shadow: 0 0 0 2px rgba(theme.$color-primary, 0.2);
            }
          }

          :deep(.el-select__selected-item) {
            color: theme.$color-text-primary;
            font-size: 14px;
          }

          :deep(.el-select__placeholder) {
            color: theme.$color-text-secondary;
            font-size: 14px;
          }
        }
      }
    }
  }

  .library-content {
    flex: 1;
    display: flex;
    min-height: 400px; // 设置合理的最小高度
    width: 100%;
    // 移除 overflow: hidden，允许内容自然扩展

    .tree-section {
      width: 280px;
      min-height: 400px; // 设置合理的最小高度
      border-right: 1px solid theme.$color-border;
      display: flex;
      flex-direction: column;
      position: relative;
      transition: all 0.3s ease;

      &.collapsed {
        width: 48px;
        min-width: 48px;
      }

      .tree-collapse-button {
        position: absolute;
        bottom: 100px;
        right: -15px;
        width: 30px;
        height: 30px;
        background: theme.$color-primary;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        color: white;
        box-shadow: 0 4px 12px rgba(theme.$color-primary, 0.4);
        transition: all 0.3s;
        z-index: 1015;

        &:hover {
          background: theme.$color-primary-accent;
          transform: scale(1.1);
          box-shadow: 0 6px 16px rgba(theme.$color-primary, 0.5);
        }

        .el-icon {
          font-size: 14px;
        }
      }

      .tree-content {
        flex: 1;
        padding: 16px;
        overflow-y: auto;

        .product-tree {
          .tree-node-wrapper {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            padding: 4px 8px;
            border-radius: theme.$border-radius-small;
            transition: all 0.2s ease;

            &:hover {
              background-color: rgba(theme.$color-primary, 0.08);
            }

            // 选中状态样式 - 统一产品库和备选库的选中样式
            &.selected {
              background-color: rgba(theme.$color-primary, 0.15);
              border: 1px solid rgba(theme.$color-primary, 0.3);
              box-shadow: 0 2px 4px rgba(theme.$color-primary, 0.1);

              .tree-node .node-label {
                color: theme.$color-primary;
                font-weight: 600;
              }

              &:hover {
                background-color: rgba(theme.$color-primary, 0.2);
              }
            }

            .tree-node {
              display: flex;
              align-items: center;
              gap: 8px;
              flex: 1;

              // 向左箭头按钮样式
              .move-to-product-btn {
                margin-right: 4px;
                padding: 2px 4px;
                min-width: 24px;
                height: 24px;
                border-radius: 4px;
                background-color: theme.$color-primary;
                border: 1px solid theme.$color-primary;
                transition: all 0.2s ease;

                &:hover {
                  background-color: darken(theme.$color-primary, 10%);
                  border-color: darken(theme.$color-primary, 10%);
                  transform: translateX(-2px);
                }

                .el-icon {
                  font-size: 12px;
                  color: white;
                }
              }

              .node-icon {
                font-size: 16px;

                &.system-icon {
                  color: theme.$color-primary;
                }

                &.function-icon {
                  color: #67c23a;
                }

                &.test-item-icon {
                  color: #e6a23c;
                }

                &.test-case-icon {
                  color: #909399;
                }

                &.selected-icon {
                  color: #409eff;
                  margin-left: 4px;
                  animation: pulse 1.5s infinite;
                }
              }

              .node-label {
                color: theme.$color-text-primary;
                font-size: 14px;
                font-weight: 500;

                &.deleted-case {
                  color: #999;
                  text-decoration: line-through;
                  opacity: 0.6;
                }
              }

              .inline-edit-input {
                width: 150px;
                font-size: 14px;
              }
            }

            .node-actions {
              display: flex;
              gap: 4px;
              opacity: 0.8;

              .el-button {
                padding: 2px 4px;
                min-height: 20px;
                font-size: 12px;

                &.el-button--primary {
                  background-color: theme.$color-primary;
                  border-color: theme.$color-primary;
                }

                &.el-button--danger {
                  background-color: #f56c6c;
                  border-color: #f56c6c;
                }
              }
            }
          }

          :deep(.el-tree-node__content) {
            padding: 2px 0;
            border-radius: theme.$border-radius-small;
            background: transparent !important;

            &:hover {
              background: transparent !important;
            }
          }

          :deep(.el-tree-node__expand-icon) {
            color: theme.$color-text-secondary;
            font-size: 14px;
          }

          :deep(.el-tree-node.is-current > .el-tree-node__content) {
            background-color: rgba(theme.$color-primary, 0.15);
            color: theme.$color-primary;
          }
        }
      }
    }

    // 用例详情表单样式
    .usecase-detail-section {
      flex: 1;
      min-width: 0; // 防止flex子项溢出
      min-height: 400px; // 设置合理的最小高度
      border-left: 1px solid theme.$color-border;
      display: flex;
      flex-direction: column;
      // 移除 overflow: hidden，允许内容根据需要扩展

      .usecase-detail-content {
        flex: 1;
        padding: 16px;
        // 移除滚动条和高度限制，让内容自然扩展
        width: 100%; // 确保宽度不超过父容器
        box-sizing: border-box; // 包含padding在宽度计算中

        .el-form {
          width: 100%;
          max-width: 100%; // 确保表单不超过容器宽度

          .form-row {
            display: flex;
            gap: 16px;
            margin-bottom: 16px; // 减少间距，与单个表单项保持一致
            width: 100%;
            align-items: center; // 确保并排输入框垂直居中对齐

            .form-item-half {
              flex: 1;
              min-width: 0; // 防止flex子项溢出
              margin-bottom: 0;
              display: flex;
              align-items: center; // 表单项内部垂直居中

              // 确保输入框与标签垂直居中对齐
              :deep(.el-form-item__content) {
                display: flex;
                align-items: center;
                flex: 1;
              }
            }
          }

          .el-form-item {
            margin-bottom: 16px; // 减少间距，与并排表单项保持一致
            width: 100%;
            max-width: 100%; // 确保表单项不超过容器宽度

            .el-form-item__label {
              font-weight: normal; // 移除加粗效果
              color: theme.$color-text-primary;
              line-height: 1.4;
              font-size: 13px; // 保持适中的字体大小
            }

            .el-input,
            .el-textarea {
              width: 100%;
              max-width: 100%; // 确保输入框不超过容器宽度

              .el-input__wrapper,
              .el-textarea__inner {
                border-radius: theme.$border-radius-small;
                transition: all theme.$transition-fast;
                font-size: 13px;
                width: 100%;
                box-sizing: border-box; // 包含边框和padding在宽度计算中

                &:hover {
                  border-color: theme.$color-primary;
                }
              }

              // 专门为textarea自适应高度优化
              &.el-textarea {
                .el-textarea__inner {
                  resize: none; // 禁用手动调整大小
                  overflow: hidden; // 配合autosize使用
                  min-height: 32px; // 设置最小高度
                  line-height: 1.4; // 合适的行高
                }

                &:focus {
                  border-color: theme.$color-primary;
                  box-shadow: 0 0 0 2px rgba(theme.$color-primary, 0.2);
                }
              }
            }

            .readonly-input {
              .el-input__wrapper {
                background-color: theme.$color-background-lighter-2;
                color: theme.$color-text-secondary;
                cursor: not-allowed;

                .el-input__inner {
                  cursor: not-allowed;
                  background-color: transparent;
                }
              }
            }
          }

          // 测试步骤表格样式
          .test-steps-table {
            width: 100%;
            max-width: 100%; // 确保不超过容器宽度
            overflow-x: auto; // 允许横向滚动
            overflow-y: visible; // 允许垂直内容显示

            .table-actions {
              margin-bottom: 12px;
              display: flex;
              gap: 8px;
              align-items: center;
            }

            .test-steps-table-content {
              width: 100%;
              border-radius: theme.$border-radius-small;
              border: 1px solid theme.$color-border;
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
              margin-bottom: 16px;

              // 确保表格内容正确显示
              :deep(.el-table__body-wrapper) {
                overflow-x: auto; // 允许水平滚动
              }

              // 优化表格整体样式
              :deep(.el-table) {
                background-color: theme.$color-container;
                min-width: 1150px; // 根据列宽计算的最小宽度，确保在小容器中触发横向滚动

                &::before {
                  display: none; // 移除Element Plus默认的底部边框
                }
              }

              // 优化表格单元格样式
              :deep(.el-table__cell) {
                padding: 4px 6px; // 减少50%的内边距，使表格更紧凑
                vertical-align: top;
                word-wrap: break-word;
                line-height: 1.2; // 减少行高，使表格更紧凑
              }

              // 选择列和序号列居中对齐
              :deep(.el-table__body) {
                .el-table__row {
                  .el-table__cell:nth-child(1), // 选择列
                  .el-table__cell:nth-child(2) {
                    // 序号列
                    text-align: center !important; // 水平居中
                    vertical-align: middle !important; // 垂直居中

                    // 选择列特殊处理，确保单选框完全居中
                    &:nth-child(1) {
                      .cell {
                        display: flex !important;
                        align-items: center !important;
                        justify-content: center !important;

                        .el-radio {
                          margin: 0 !important;
                          display: flex !important;
                          align-items: center !important;
                          justify-content: center !important;
                        }
                      }
                    }

                    // 序号列特殊处理，确保数字完全居中
                    &:nth-child(2) {
                      .cell {
                        display: flex !important;
                        align-items: center !important;
                        justify-content: center !important;
                        font-weight: 500; // 序号字体稍微加粗
                      }
                    }
                  }
                }
              }

              // 特别优化表格内的输入框和文本区域
              :deep(.el-table__cell .el-input),
              :deep(.el-table__cell .el-textarea) {
                margin: 2px 0; // 减少输入框上下边距，使表格更紧凑
                width: 100%; // 确保输入框占满单元格宽度
              }

              // 优化表格内的文本显示
              :deep(.el-table__cell .cell) {
                padding: 0 !important; // 移除cell内部的默认padding
                line-height: 1.2 !important; // 减少行高，使表格更紧凑
                min-height: 24px; // 减少最小高度50%，使表格更紧凑
                display: flex;
                align-items: flex-start; // 顶部对齐
              }

              // 专门优化选择列的单选框居中
              :deep(.el-table__cell:first-child) {
                .el-radio {
                  width: 100% !important;
                  height: 100% !important;
                  display: flex !important;
                  align-items: center !important;
                  justify-content: center !important;
                  margin: 0 !important;

                  .el-radio__input {
                    margin: 0 !important;
                  }

                  .el-radio__label {
                    display: none !important; // 隐藏单选框的文字标签
                  }
                }
              }

              // 确保表格列宽度合理分配
              :deep(.el-table) {
                table-layout: fixed !important; // 强制固定表格布局
                width: 100% !important;
              }

              // 优化单选框列和序号列的显示
              :deep(.el-table__cell:first-child), // 选择列
              :deep(.el-table__cell:nth-child(2)) {
                // 序号列
                text-align: center !important; // 水平居中显示
                vertical-align: middle !important; // 垂直居中显示
                padding: 4px 8px !important; // 减少50%的内边距

                // 确保选择列和序号列内容完全居中
                .cell {
                  display: flex !important;
                  align-items: center !important; // 垂直居中
                  justify-content: center !important; // 水平居中
                  height: 100% !important;
                  min-height: 24px !important;
                }

                // 选择列单选框特殊处理
                &:first-child {
                  .el-radio {
                    display: flex !important;
                    align-items: center !important;
                    justify-content: center !important;
                    margin: 0 !important; // 移除默认边距

                    .el-radio__input {
                      margin: 0 !important;
                    }
                  }
                }
              }

              :deep(.el-table__header) {
                background-color: theme.$color-container-lighter-3;

                th {
                  background-color: theme.$color-container-lighter-3;
                  color: theme.$color-text-primary;
                  font-weight: 600;
                  font-size: 13px;
                  border-bottom: 2px solid theme.$color-border; // 增加底部边框厚度
                  border-right: 1px solid rgba(theme.$color-border, 0.6); // 添加右侧边框
                  padding: 6px 8px !important; // 减少头部内边距，使表格更紧凑
                  height: 32px !important; // 减少头部高度50%，使表格更紧凑
                  line-height: 1.2;
                  text-align: center !important; // 所有表头字段都居中对齐
                  vertical-align: middle !important; // 垂直居中对齐

                  // 确保表头内容完全居中
                  .cell {
                    display: flex !important;
                    align-items: center !important;
                    justify-content: center !important;
                    height: 100% !important;
                  }

                  &:last-child {
                    border-right: none; // 最后一列不显示右边框
                  }
                }
              }

              // 优化表格内textarea样式
              :deep(.el-textarea) {
                .el-textarea__inner {
                  padding: 3px 6px; // 减少内边距，使表格更紧凑
                  font-size: 12px;
                  resize: none;
                  min-height: 24px; // 减少最小高度，使表格更紧凑
                  line-height: 1.3;
                  text-align: left; // 确保输入框内容左对齐
                  overflow: hidden; // 配合autosize使用
                }
              }

              :deep(.el-table__body) {
                tr {
                  transition: background-color 0.2s ease;

                  &:hover {
                    background-color: rgba(theme.$color-primary, 0.04);
                  }

                  td {
                    border-bottom: 1px solid rgba(theme.$color-border, 0.3);
                    padding: 4px 8px !important; // 减少内边距50%，使表格更紧凑
                    vertical-align: middle !important;
                    height: auto !important;

                    // 选择列和序号列样式
                    &:nth-child(1),
                    &:nth-child(2) {
                      text-align: center !important;
                      vertical-align: middle !important;
                      padding: 4px !important;

                      .cell {
                        display: flex !important;
                        align-items: center !important;
                        justify-content: center !important;
                        height: 100% !important;
                      }
                    }

                    // 输入框样式优化
                    .el-input,
                    .el-textarea {
                      .el-input__wrapper,
                      .el-textarea__inner {
                        box-shadow: none !important;
                        border: 1px solid transparent !important;
                        background-color: transparent !important;
                        padding: 4px 8px !important;
                        min-height: auto !important;
                        font-size: 13px !important;

                        &:hover,
                        &:focus,
                        &.is-focus {
                          border-color: theme.$color-primary !important;
                          background-color: rgba(theme.$color-primary, 0.02) !important;
                        }
                      }

                      .el-textarea__inner {
                        resize: none !important;
                        min-height: 28px !important;
                        line-height: 1.4 !important;
                      }
                    }

                    // 单选按钮样式
                    .el-radio {
                      margin: 0 !important;

                      .el-radio__input {
                        margin: 0 !important;
                      }
                    }
                  }
                }
              }

              :deep(.el-table__border-left-patch) {
                background-color: theme.$color-container-lighter-3;
              }

              :deep(.el-table__border-right-patch) {
                background-color: theme.$color-container-lighter-3;
              }

              :deep(.el-table__border-left-patch) {
                background-color: theme.$color-container-lighter-3;
              }

              :deep(.el-table__border-right-patch) {
                background-color: theme.$color-container-lighter-3;
              }
            }

            // 响应式表格样式
            @media (max-width: 1400px) {
              .test-steps-table-content {
                font-size: 12px; // 在较小屏幕上减小字体

                :deep(.el-table__cell) {
                  padding: 10px 6px !important; // 适中的单元格内边距
                }

                :deep(.el-table__header th) {
                  padding: 10px 6px !important; // 头部单元格适中内边距
                  height: 40px !important; // 适中高度
                }

                :deep(.el-textarea .el-textarea__inner) {
                  font-size: 12px;
                  padding: 6px 8px; // 调整textarea内边距
                  min-height: 38px;
                }

                :deep(.el-input__inner) {
                  font-size: 12px;
                }
              }
            }

            @media (max-width: 1024px) {
              .test-steps-table-content {
                // 在更小的屏幕上，允许水平滚动
                overflow-x: auto;
                min-width: 800px; // 设置最小宽度，保证表格可读性

                :deep(.el-table__cell) {
                  padding: 8px 6px !important; // 小屏幕合理内边距
                }

                :deep(.el-textarea .el-textarea__inner) {
                  font-size: 11px;
                  padding: 5px 6px;
                  min-height: 35px;
                }
              }
            }

            // 固定操作按钮样式
            .table-actions {
              margin-bottom: 12px; // 改为下边距，因为按钮现在在表格上方
              display: flex;
              gap: 8px;
              align-items: center;
              justify-content: flex-start; // 改为居左显示
              padding-left: 45px; // 与单选框列对齐（单选框列宽度为45px）

              .action-btn {
                width: 19px; // 缩小40%：32px * 0.6 ≈ 19px
                height: 19px;
                padding: 0;
                border: none;
                box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1); // 相应缩小阴影
                transition: all 0.3s ease;

                &.add-btn {
                  background-color: theme.$color-primary;
                  color: white;

                  &:hover {
                    background-color: theme.$color-primary-accent;
                    transform: scale(1.1);
                    box-shadow: 0 4px 12px rgba(theme.$color-primary, 0.4);
                  }
                }

                &.remove-btn {
                  background-color: #f56c6c;
                  color: white;

                  &:hover:not(:disabled) {
                    background-color: darken(#f56c6c, 10%);
                    transform: scale(1.1);
                    box-shadow: 0 4px 12px rgba(#f56c6c, 0.4);
                  }

                  &:disabled {
                    opacity: 0.5;
                    cursor: not-allowed;
                    transform: none;
                    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
                  }
                }

                .el-icon {
                  font-size: 10px; // 相应缩小图标尺寸
                }
              }
            }
          }

          // 表单行布局样式（统一与主要样式一致）
          .form-row {
            display: flex;
            gap: 16px; // 统一gap为16px，与主要样式保持一致
            margin-bottom: 16px; // 统一margin-bottom，确保与单个表单项间距一致
            width: 100%;
            align-items: center; // 统一垂直居中对齐

            .form-item-half {
              flex: 1;
              min-width: 0; // 防止flex子项溢出
              margin-bottom: 0; // 设为0，因为父容器已设置margin-bottom
              display: flex;
              align-items: center; // 表单项内部垂直居中

              // 确保输入框与标签垂直居中对齐
              :deep(.el-form-item__content) {
                display: flex;
                align-items: center;
                flex: 1;
              }

              :deep(.el-form-item__label) {
                width: 80px; // 缩短标签宽度以适应缩短后的标签名称
                text-align: right;
                padding-right: 12px;
                font-size: 13px; // 保持适中的字体大小
                color: theme.$color-text-primary;
                font-weight: normal; // 移除加粗效果
              }
            }
          }
        }
      }

      .empty-state {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 40px;
        min-height: 300px; // 确保有足够的高度来显示居中效果
        text-align: center; // 确保文本居中

        // 确保 el-empty 组件完全居中
        :deep(.el-empty) {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          width: 100%;

          .el-empty__image {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;

            svg {
              display: block;
              margin: 0 auto;
            }
          }

          .el-empty__description {
            text-align: center;
            margin-top: 16px;
            color: theme.$color-text-secondary;
            font-size: 14px;
          }

          // 响应式设计 - 与产品库保持一致
          @media (max-width: 1024px) {
            .test-steps-table-content {
              // 在更小的屏幕上，允许水平滚动
              overflow-x: auto;
              min-width: 800px; // 设置最小宽度，保证表格可读性

              :deep(.el-table__cell) {
                padding: 8px 6px !important; // 小屏幕合理内边距
              }

              :deep(.el-table__header th) {
                padding: 4px 6px !important;
                font-size: 12px !important;
              }
            }
          }

          @media (max-width: 768px) {
            .test-steps-table-content {
              min-width: 600px; // 移动端进一步减少最小宽度

              :deep(.el-table__cell) {
                padding: 6px 4px !important;
              }

              :deep(.el-table__header th) {
                padding: 3px 4px !important;
                font-size: 11px !important;
              }
            }
          }
        }
      }
    }
  }
}

// 导出对话框样式
.export-dialog-content {
  .export-checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding: 16px 0;

    .export-checkbox {
      margin: 0;

      :deep(.el-checkbox__label) {
        font-size: 14px;
        color: theme.$color-text-secondary;
        font-weight: 500;
      }
    }
  }
}

.export-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;

  .el-button {
    font-size: 14px;
    padding: 8px 16px;
  }
}

// 备选库样式（与产品库保持一致）
.candidate-library {
  // 备选库使用与产品库相同的flex布局
  flex: 1;
}

.attachment-section {
  width: 300px;
  background: theme.$color-container;
  border: 1px solid theme.$color-border;
  border-radius: theme.$border-radius-medium;
  box-shadow: theme.$shadow-soft;
  display: flex;
  flex-direction: column;

  .section-header {
    padding: 16px;
    border-bottom: 1px solid theme.$color-border;
    background: linear-gradient(
      135deg,
      theme.$color-container 0%,
      theme.$color-container-lighter-2 100%
    );
    border-radius: theme.$border-radius-medium theme.$border-radius-medium 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;

    h3 {
      margin: 0;
      font-size: 16px;
      color: theme.$color-text-primary;
      font-weight: 600;
    }
  }

  .attachment-content {
    flex: 1;
    padding: 16px;
    overflow-y: auto;

    .attachment-list {
      display: flex;
      flex-direction: column;
      gap: 8px;

      .attachment-item {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px;
        background: theme.$color-container-lighter-1;
        border: 1px solid theme.$color-border;
        border-radius: theme.$border-radius-small;
        cursor: pointer;
        transition: all theme.$transition-fast;

        &:hover {
          background-color: rgba(theme.$color-primary, 0.1);
          border-color: theme.$color-primary;
          transform: translateY(-1px);
          box-shadow: theme.$shadow-soft;
        }

        .attachment-name {
          flex: 1;
          color: theme.$color-text-primary;
          font-size: 14px;
        }

        .remove-btn {
          opacity: 0;
          transition: opacity theme.$transition-fast;
        }

        &:hover .remove-btn {
          opacity: 1;
        }
      }
    }

    .empty-attachments {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 200px;
    }
  }
}

// 对话框样式
:deep(.el-dialog) {
  background-color: theme.$color-container;
  border-radius: theme.$border-radius-large;
  box-shadow: theme.$shadow-strong;

  .el-dialog__header {
    background: linear-gradient(
      135deg,
      theme.$color-container-lighter-2 0%,
      theme.$color-container 100%
    );
    border-bottom: 1px solid theme.$color-border;
    border-radius: theme.$border-radius-large theme.$border-radius-large 0 0;
    padding: 20px 24px;

    .el-dialog__title {
      color: theme.$color-text-primary;
      font-weight: 600;
    }
  }

  .el-dialog__body {
    padding: 24px;
  }

  .el-dialog__footer {
    padding: 16px 24px;
    border-top: 1px solid theme.$color-border;
  }
}

// 备选库区域样式（与产品库保持完全一致）
.candidate-library-section {
  min-width: 0; // 防止内容溢出
  min-height: 600px; // 与产品库保持一致的最小高度
  height: auto; // 改为自动高度，允许内容扩展
  background: theme.$color-container;
  border: 1px solid theme.$color-border;
  border-radius: theme.$border-radius-medium;
  box-shadow: theme.$shadow-soft;
  display: flex;
  flex-direction: column;
  // 移除 overflow: hidden，允许内容根据需要扩展

  .section-header {
    padding: 16px;
    border-bottom: 1px solid theme.$color-border;
    background: linear-gradient(
      135deg,
      theme.$color-container 0%,
      theme.$color-container-lighter-2 100%
    );
    border-radius: theme.$border-radius-medium theme.$border-radius-medium 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 56px; // 与产品库保持一致的标题栏高度

    h3 {
      margin: 0;
      font-size: 16px;
      color: theme.$color-text-primary;
      font-weight: 600;
      line-height: 1.4; // 确保文字垂直居中
    }

    .header-actions {
      display: flex;
      gap: 8px;

      .collapse-btn {
        width: 28px;
        height: 28px;
        background-color: rgba(theme.$color-primary, 0.1);
        border: 1px solid rgba(theme.$color-primary, 0.3);
        color: theme.$color-primary;
        transition: all 0.3s ease;

        &:hover {
          background-color: rgba(theme.$color-primary, 0.2);
          border-color: theme.$color-primary;
          transform: scale(1.1);
        }

        .el-icon {
          font-size: 14px;
        }
      }
    }
  }

  // 折叠状态下的窄条样式（备选库专用，右侧折叠）
  .collapsed-strip {
    width: 100%;
    height: 100%;
    background: linear-gradient(
      135deg,
      theme.$color-container 0%,
      theme.$color-container-lighter-2 100%
    );
    border: 1px solid theme.$color-border;
    border-radius: theme.$border-radius-medium;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    .expand-button {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 8px;
      padding: 16px 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      border-radius: theme.$border-radius-small;
      width: 100%;
      height: 120px;

      &:hover {
        background-color: rgba(theme.$color-primary, 0.1);
        transform: scale(1.05);
      }

      .expand-icon {
        font-size: 20px;
        color: theme.$color-primary;
        transition: all 0.3s ease;
        // 备选库使用向左展开的图标
        transform: rotate(180deg);
      }

      .strip-label {
        font-size: 12px;
        color: theme.$color-text-secondary;
        writing-mode: vertical-rl; // 垂直文字
        text-orientation: mixed;
        font-weight: 500;
        letter-spacing: 2px;
      }

      &:hover .expand-icon {
        color: theme.$color-primary-accent;
        transform: rotate(180deg) scale(1.2);
      }

      &:hover .strip-label {
        color: theme.$color-text-primary;
      }
    }
  }

  .search-section {
    padding: 16px;
    border-bottom: 1px solid theme.$color-border;
    background: theme.$color-container-lighter-1;
    min-height: 72px; // 与产品库保持一致的搜索区域高度

    .search-controls {
      display: flex;
      align-items: center;
      gap: 12px;
      height: 40px; // 固定控件高度
    }
  }

  .version-management-section {
    padding: 16px;
    border-bottom: 1px solid theme.$color-border;
    background: linear-gradient(
      135deg,
      theme.$color-container-lighter-2 0%,
      theme.$color-container-lighter-1 100%
    );
    min-height: 80px; // 与产品库保持一致的版本管理区域高度

    .version-controls {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 16px;
      min-height: 48px; // 确保控件区域高度一致

      .version-actions {
        display: flex;
        gap: 8px;
        align-items: center;

        // 确保备选库的版本按钮与产品库样式完全一致
        .version-btn {
          transition: all theme.$transition-fast;

          &.nav-btn {
            background-color: theme.$color-primary;
            border-color: theme.$color-primary;
            color: white;

            &:hover:not(:disabled) {
              background-color: theme.$color-primary-darker;
              border-color: theme.$color-primary-darker;
              transform: translateY(-1px);
              box-shadow: 0 4px 8px rgba(theme.$color-primary, 0.3);
            }

            &:disabled {
              opacity: 0.5;
              cursor: not-allowed;
            }
          }
        }
      }

      .version-selector {
        display: flex;
        align-items: center;
        gap: 12px;

        .version-label {
          font-size: 14px;
          color: theme.$color-text-secondary;
          font-weight: 500;
        }

        .version-select {
          min-width: 180px;
          max-width: 250px;
        }
      }
    }
  }

  .library-content {
    flex: 1;
    display: flex;
    min-height: 0; // 允许flex子项收缩
    // 移除 overflow: hidden，允许内容自然扩展

    .tree-section {
      width: 300px;
      min-width: 250px;
      max-width: 400px;
      border-right: 1px solid theme.$color-border;
      background: theme.$color-container-lighter-1;
      display: flex;
      flex-direction: column;
      position: relative;
      // 移除固定高度，允许内容自然扩展

      &.collapsed {
        width: 40px;
        min-width: 40px;
        max-width: 40px;

        .tree-content {
          display: none;
        }
      }

      .tree-collapse-button {
        position: absolute;
        top: 50%;
        right: -12px;
        transform: translateY(-50%);
        width: 24px;
        height: 24px;
        background: theme.$color-container;
        border: 1px solid theme.$color-border;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        z-index: 10;
        box-shadow: theme.$shadow-soft;
        transition: all 0.3s ease;

        &:hover {
          background: theme.$color-primary-light;
          border-color: theme.$color-primary;
          transform: translateY(-50%) scale(1.1);
        }

        .el-icon {
          font-size: 12px;
          color: theme.$color-text-secondary;
        }
      }

      .tree-content {
        flex: 1;
        padding: 16px;
        // 移除固定高度，允许内容自然扩展
        overflow-y: auto; // 内容过多时显示滚动条

        .product-tree {
          .tree-node-wrapper {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            padding: 4px 8px;
            border-radius: theme.$border-radius-small;
            transition: all 0.2s ease;

            &:hover {
              background-color: rgba(theme.$color-primary, 0.08);
            }

            // 选中状态样式 - 与产品库保持完全一致
            &.selected {
              background-color: rgba(theme.$color-primary, 0.15);
              border: 1px solid rgba(theme.$color-primary, 0.3);
              box-shadow: 0 2px 4px rgba(theme.$color-primary, 0.1);

              .tree-node .node-label {
                color: theme.$color-primary;
                font-weight: 600;
              }

              &:hover {
                background-color: rgba(theme.$color-primary, 0.2);
              }
            }

            .tree-node {
              display: flex;
              align-items: center;
              gap: 8px;
              flex: 1;

              // 向左箭头按钮样式
              .move-to-product-btn {
                margin-right: 4px;
                padding: 2px 4px;
                min-width: 24px;
                height: 24px;
                border-radius: 4px;
                background-color: theme.$color-primary;
                border: 1px solid theme.$color-primary;
                transition: all 0.2s ease;

                &:hover {
                  background-color: darken(theme.$color-primary, 10%);
                  border-color: darken(theme.$color-primary, 10%);
                  transform: translateX(-2px);
                }

                .el-icon {
                  font-size: 12px;
                  color: white;
                }
              }

              .node-icon {
                font-size: 16px;

                &.system-icon {
                  color: theme.$color-primary;
                }

                &.function-icon {
                  color: #67c23a;
                }

                &.test-item-icon {
                  color: #e6a23c;
                }

                &.test-case-icon {
                  color: #909399;
                }

                &.selected-icon {
                  color: #409eff;
                  margin-left: 4px;
                  animation: pulse 1.5s infinite;
                }
              }

              .node-label {
                color: theme.$color-text-primary;
                font-size: 14px;
                font-weight: 500;

                &.deleted-case {
                  color: #999;
                  text-decoration: line-through;
                  opacity: 0.6;
                }
              }

              .inline-edit-input {
                width: 150px;
                font-size: 14px;
              }
            }

            .node-actions {
              display: flex;
              gap: 4px;
              opacity: 0;
              transition: opacity 0.2s ease;

              .el-button {
                width: 20px;
                height: 20px;
                padding: 0;
                border: none;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.2s ease;

                .el-icon {
                  font-size: 12px;
                }
              }
            }

            &:hover .node-actions {
              opacity: 1;
            }
          }
        }
      }
    }

    .usecase-detail-section {
      flex: 1;
      display: flex;
      flex-direction: column;
      min-width: 0; // 防止flex子项溢出
      background: theme.$color-container-lighter-1;
      // 移除固定高度，允许内容自然扩展

      .usecase-detail-content {
        flex: 1;
        padding: 24px;
        // 移除滚动条和高度限制，让内容自然扩展

        // 备选库测试步骤表格样式 - 与产品库保持完全一致
        .test-steps-table {
          width: 100%;
          max-width: 100%; // 确保不超过容器宽度
          overflow-x: auto; // 允许横向滚动
          overflow-y: visible; // 允许垂直内容显示

          .table-actions {
            margin-bottom: 12px;
            display: flex;
            gap: 8px;
            align-items: center;
            justify-content: flex-start;
            padding-left: 45px;

            .action-btn {
              width: 19px;
              height: 19px;
              padding: 0;
              border: none;
              box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
              transition: all 0.3s ease;

              &.add-btn {
                background-color: theme.$color-primary;
                color: white;

                &:hover {
                  background-color: theme.$color-primary-accent;
                  transform: scale(1.1);
                  box-shadow: 0 4px 12px rgba(theme.$color-primary, 0.4);
                }
              }

              &.remove-btn {
                background-color: #f56c6c;
                color: white;

                &:hover:not(:disabled) {
                  background-color: darken(#f56c6c, 10%);
                  transform: scale(1.1);
                  box-shadow: 0 4px 12px rgba(#f56c6c, 0.4);
                }

                &:disabled {
                  opacity: 0.5;
                  cursor: not-allowed;
                  transform: none;
                  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
                }
              }

              .el-icon {
                font-size: 10px;
              }
            }
          }

          // 添加测试步骤表格内容样式，与产品库保持一致
          .test-steps-table-content {
            width: 100%;
            border-radius: theme.$border-radius-small;
            border: 1px solid theme.$color-border;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            margin-bottom: 16px;

            // 确保表格内容正确显示，支持横向滚动
            :deep(.el-table__body-wrapper) {
              overflow-x: auto; // 允许水平滚动
            }

            // 设置表格最小宽度，确保在小容器中触发横向滚动
            :deep(.el-table) {
              min-width: 1150px; // 根据列宽计算的最小宽度
            }

            // 优化表格整体样式
            :deep(.el-table) {
              background-color: theme.$color-container;

              &::before {
                display: none; // 移除Element Plus默认的底部边框
              }
            }

            // 优化表格单元格样式
            :deep(.el-table__cell) {
              padding: 4px 6px; // 减少50%的内边距，使表格更紧凑
              vertical-align: top;
              word-wrap: break-word;
              line-height: 1.2; // 减少行高，使表格更紧凑
            }

            // 选择列和序号列居中对齐
            :deep(.el-table__body) {
              .el-table__row {
                .el-table__cell:nth-child(1), // 选择列
                .el-table__cell:nth-child(2) {
                  // 序号列
                  text-align: center !important; // 水平居中
                  vertical-align: middle !important; // 垂直居中

                  // 选择列特殊处理，确保单选框完全居中
                  &:nth-child(1) {
                    .cell {
                      display: flex !important;
                      align-items: center !important;
                      justify-content: center !important;

                      .el-radio {
                        margin: 0 !important;
                        display: flex !important;
                        align-items: center !important;
                        justify-content: center !important;
                      }
                    }
                  }

                  // 序号列特殊处理，确保数字完全居中
                  &:nth-child(2) {
                    .cell {
                      display: flex !important;
                      align-items: center !important;
                      justify-content: center !important;
                      font-weight: 500; // 序号字体稍微加粗
                    }
                  }
                }
              }
            }

            // 特别优化表格内的输入框和文本区域
            :deep(.el-table__cell .el-input),
            :deep(.el-table__cell .el-textarea) {
              margin: 2px 0; // 减少输入框上下边距，使表格更紧凑
              width: 100%; // 确保输入框占满单元格宽度
            }

            // 优化表格内的文本显示
            :deep(.el-table__cell .cell) {
              padding: 0 !important; // 移除cell内部的默认padding
              line-height: 1.2 !important; // 减少行高，使表格更紧凑
              min-height: 24px; // 减少最小高度50%，使表格更紧凑
              display: flex;
              align-items: flex-start; // 顶部对齐
            }

            // 专门优化选择列的单选框居中
            :deep(.el-table__cell:first-child) {
              .el-radio {
                width: 100% !important;
                height: 100% !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                margin: 0 !important;

                .el-radio__input {
                  margin: 0 !important;
                }

                .el-radio__label {
                  display: none !important; // 隐藏单选框的文字标签
                }
              }
            }

            // 确保表格列宽度合理分配
            :deep(.el-table) {
              table-layout: fixed !important; // 强制固定表格布局
              width: 100% !important;
            }

            // 优化单选框列和序号列的显示
            :deep(.el-table__cell:first-child), // 选择列
            :deep(.el-table__cell:nth-child(2)) {
              // 序号列
              text-align: center !important; // 水平居中显示
              vertical-align: middle !important; // 垂直居中显示
              padding: 4px 8px !important; // 减少50%的内边距

              // 确保选择列和序号列内容完全居中
              .cell {
                display: flex !important;
                align-items: center !important; // 垂直居中
                justify-content: center !important; // 水平居中
                height: 100% !important;
                min-height: 24px !important;
              }

              // 选择列单选框特殊处理
              &:first-child {
                .el-radio {
                  display: flex !important;
                  align-items: center !important;
                  justify-content: center !important;
                  margin: 0 !important; // 移除默认边距

                  .el-radio__input {
                    margin: 0 !important;
                  }
                }
              }
            }
          }

          .el-table {
            border: 1px solid theme.$color-border;
            border-radius: theme.$border-radius-medium;

            // 与产品库保持完全一致的表头样式
            :deep(.el-table__header) {
              background-color: theme.$color-container-lighter-3;

              th {
                background-color: theme.$color-container-lighter-3;
                color: theme.$color-text-primary;
                font-weight: 600;
                font-size: 13px;
                border-bottom: 2px solid theme.$color-border;
                border-right: 1px solid rgba(theme.$color-border, 0.6);
                padding: 6px 8px !important;
                height: 32px !important;
                line-height: 1.2;
                text-align: center !important; // 所有表头字段都居中对齐
                vertical-align: middle !important; // 垂直居中对齐

                // 确保表头内容完全居中
                .cell {
                  display: flex !important;
                  align-items: center !important;
                  justify-content: center !important;
                  height: 100% !important;
                }

                &:last-child {
                  border-right: none;
                }
              }
            }

            // 与产品库保持完全一致的表格体样式
            :deep(.el-table__body) {
              tr {
                transition: background-color 0.2s ease;

                &:hover {
                  background-color: rgba(theme.$color-primary, 0.04);
                }

                td {
                  border-bottom: 1px solid rgba(theme.$color-border, 0.3);
                  padding: 4px 8px !important; // 减少内边距50%，使表格更紧凑
                  vertical-align: middle !important;
                  height: auto !important;

                  // 选择列和序号列样式
                  &:nth-child(1),
                  &:nth-child(2) {
                    text-align: center !important;
                    vertical-align: middle !important;
                    padding: 4px !important;

                    .cell {
                      display: flex !important;
                      align-items: center !important;
                      justify-content: center !important;
                      height: 100% !important;
                    }
                  }

                  // 输入框样式优化
                  .el-input,
                  .el-textarea {
                    .el-input__wrapper,
                    .el-textarea__inner {
                      box-shadow: none !important;
                      border: 1px solid transparent !important;
                      background-color: transparent !important;
                      padding: 4px 8px !important;
                      min-height: auto !important;
                      font-size: 13px !important;

                      &:hover,
                      &:focus,
                      &.is-focus {
                        border-color: theme.$color-primary !important;
                        background-color: rgba(theme.$color-primary, 0.02) !important;
                      }
                    }

                    .el-textarea__inner {
                      resize: none !important;
                      min-height: 28px !important;
                      line-height: 1.4 !important;
                    }
                  }

                  // 单选按钮样式
                  .el-radio {
                    margin: 0 !important;

                    .el-radio__input {
                      margin: 0 !important;
                    }
                  }
                }
              }
            }
          }
          // 响应式表格样式 - 与产品库保持完全一致
          @media (max-width: 1400px) {
            .test-steps-table-content {
              font-size: 12px; // 在较小屏幕上减小字体

              :deep(.el-table__cell) {
                padding: 10px 6px !important; // 适中的单元格内边距
              }

              :deep(.el-table__header th) {
                padding: 10px 6px !important; // 头部单元格适中内边距
                height: 40px !important; // 适中高度
              }

              :deep(.el-textarea .el-textarea__inner) {
                font-size: 12px;
                padding: 6px 8px; // 调整textarea内边距
                min-height: 38px;
              }

              :deep(.el-input__inner) {
                font-size: 12px;
              }
            }
          }

          @media (max-width: 1024px) {
            .test-steps-table-content {
              // 在更小的屏幕上，允许水平滚动
              overflow-x: auto;
              min-width: 800px; // 设置最小宽度，保证表格可读性

              :deep(.el-table__cell) {
                padding: 8px 6px !important; // 小屏幕合理内边距
              }

              :deep(.el-table__header th) {
                padding: 4px 6px !important;
                font-size: 12px !important;
              }
            }
          }

          @media (max-width: 768px) {
            .test-steps-table-content {
              min-width: 600px; // 移动端进一步减少最小宽度

              :deep(.el-table__cell) {
                padding: 6px 4px !important;
              }

              :deep(.el-table__header th) {
                padding: 3px 4px !important;
                font-size: 11px !important;
              }
            }
          }
        }

        // 表单行布局样式（与产品库统一）
        .form-row {
          display: flex;
          gap: 16px;
          margin-bottom: 16px;
          width: 100%;
          align-items: center;

          .form-item-half {
            flex: 1;
            min-width: 0;
            margin-bottom: 0;
            display: flex;
            align-items: center;

            :deep(.el-form-item__content) {
              display: flex;
              align-items: center;
              flex: 1;
            }

            :deep(.el-form-item__label) {
              width: 80px; // 与产品库保持一致的标签宽度
              text-align: right;
              padding-right: 12px;
              font-size: 13px;
              color: theme.$color-text-primary;
              font-weight: normal;
            }
          }
        }

        .el-form-item {
          margin-bottom: 16px; // 减少间距以提高紧凑性

          .el-form-item__label {
            font-weight: normal;
            color: theme.$color-text-primary;
          }

          .readonly-input {
            background: theme.$color-container-lighter-2;
            cursor: not-allowed;
          }
        }
      }

      .empty-state {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        color: theme.$color-text-secondary;
        font-size: 16px;
      }
    }
  }
}

// 响应式设计 - 优化并排布局的响应式表现
@media (max-width: 1200px) {
  .libraries-container {
    display: flex; // 在小屏幕上回退到flex布局
    flex-direction: column; // 在较小屏幕上改为垂直布局
    gap: 16px; // 增加垂直间距

    > .product-library-section,
    > .candidate-library-section {
      grid-column: unset; // 重置grid列设置
      width: 100%;
      max-width: 100%;
      min-height: 500px; // 在垂直布局时减少最小高度
    }
  }
}

// 中等屏幕优化 - 保持并排但调整间距
@media (min-width: 1201px) and (max-width: 1400px) {
  .libraries-container {
    gap: 12px; // 减少间距以节省空间

    > .product-library-section,
    > .candidate-library-section {
      margin-left: 4px;
      margin-right: 4px;
    }
  }
}

// 平板和小屏幕优化
@media (max-width: 1024px) {
  .libraries-container {
    min-height: calc(100vh - 100px); // 减少最小高度
  }
  .product-library-section {
    .library-content {
      flex-direction: column;

      .tree-section {
        width: 100%;
        min-height: 300px; // 改为最小高度，允许内容扩展
        max-height: 50vh; // 设置最大高度，避免占用过多空间
        overflow-y: auto; // 内容过多时显示滚动条
        border-right: none;
        border-bottom: 1px solid theme.$color-border;
      }
    }
  }

  .candidate-library-section {
    .library-content {
      flex-direction: column;

      .tree-section {
        width: 100%;
        min-height: 300px; // 改为最小高度，允许内容扩展
        max-height: 50vh; // 设置最大高度，避免占用过多空间
        overflow-y: auto; // 内容过多时显示滚动条
        border-right: none;
        border-bottom: 1px solid theme.$color-border;
      }
    }
  }
}

// 移动端优化
@media (max-width: 768px) {
  .usecase-library-container {
    padding: 8px; // 减少内边距以节省空间
  }

  .libraries-container {
    gap: 12px; // 减少间距
    min-height: calc(100vh - 80px); // 进一步减少最小高度

    > .product-library-section,
    > .candidate-library-section {
      min-height: 400px; // 在移动端减少最小高度
      margin-left: 0;
      margin-right: 0;
    }
  }

  .browser-connection-section {
    .browser-controls {
      flex-wrap: wrap;
      gap: 8px;
    }
  }

  .product-library-section {
    .search-section {
      .search-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;

        .el-select,
        .el-input {
          width: 100% !important;
        }
      }
    }

    .version-management-section {
      .version-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 16px;

        .version-actions {
          justify-content: center;
        }

        .version-selector {
          flex-direction: column;
          align-items: stretch;
          gap: 8px;

          .version-label {
            text-align: center;
          }

          .version-select {
            width: 100%;
          }
        }
      }
    }

    .library-content {
      .tree-section {
        min-height: 250px; // 改为最小高度
        max-height: 40vh; // 设置最大高度
        overflow-y: auto; // 内容过多时显示滚动条
      }
    }
  }

  .candidate-library-section {
    .search-section {
      .search-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;

        .el-select,
        .el-input {
          width: 100% !important;
        }
      }
    }

    .version-management-section {
      .version-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 16px;

        .version-actions {
          justify-content: center;
        }
      }
    }

    .library-content {
      .tree-section {
        min-height: 250px; // 改为最小高度
        max-height: 40vh; // 设置最大高度
        overflow-y: auto; // 内容过多时显示滚动条
      }
    }
  }
}

/* 截图展示区域样式 */
.screenshot-gallery {
  width: 100%;
}

.screenshot-container {
  display: flex;
  flex-wrap: nowrap;
  gap: 8px;
  height: 80px;
  overflow-x: auto;
  overflow-y: hidden;
  padding: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background-color: #fafafa;
  align-items: center;
}

.screenshot-container::-webkit-scrollbar {
  height: 6px;
}

.screenshot-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.screenshot-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.screenshot-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.screenshot-item {
  position: relative;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  border-radius: 4px;
  overflow: hidden;
  transform-origin: center;
  border: 2px solid transparent;
}

.screenshot-item:hover {
  transform: scale(1.2);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.25);
  z-index: 10;
}

.screenshot-item.selected {
  border-color: #409eff;
  box-shadow: 0 0 12px rgba(64, 158, 255, 0.4);
  transform: scale(1.05);
}

.screenshot-item.unselected {
  opacity: 0.6;
  transform: scale(0.95);
}

.screenshot-item.unselected::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.screenshot-wrapper {
  position: relative;
  width: 50px;
  height: 60px;
  flex-shrink: 0;
  overflow: hidden;
  border-radius: 4px;
  background-color: #f5f5f5;
}

.screenshot-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.screenshot-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  color: white;
  padding: 4px 3px 2px;
  transform: translateY(100%);
  transition: transform 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.screenshot-item:hover .screenshot-overlay {
  transform: translateY(0);
}

.screenshot-title {
  font-size: 8px;
  font-weight: 500;
  line-height: 1.1;
  margin-bottom: 1px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.screenshot-timestamp {
  font-size: 7px;
  opacity: 0.9;
  line-height: 1;
}

.selected-icon {
  position: absolute;
  top: 2px;
  right: 2px;
  width: 16px;
  height: 16px;
  background-color: #67c23a;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 10px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  z-index: 5;
  cursor: pointer;
}

.select-button {
  position: absolute;
  top: 2px;
  right: 2px;
  width: 16px;
  height: 16px;
  background-color: rgba(64, 158, 255, 0.8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 10px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  z-index: 5;
  cursor: pointer;
  opacity: 0;
  transform: scale(0.8);
}

.screenshot-item:hover .select-button {
  opacity: 1;
  transform: scale(1);
}

.select-button:hover {
  background-color: #409eff;
  transform: scale(1.1);
}

/* 拖拽相关样式 */
.screenshot-item.dragging {
  opacity: 0.5;
  transform: scale(0.95);
  z-index: 1000;
}

.screenshot-item.drag-over {
  border: 2px dashed #409eff;
  background-color: rgba(64, 158, 255, 0.1);
}

.screenshot-item[draggable='true'] {
  cursor: grab;
}

.screenshot-item[draggable='true']:active {
  cursor: grabbing;
}

/* 模态框样式 */
.screenshot-modal {
  .el-dialog {
    max-width: 90vw;
    max-height: 90vh;
  }

  .el-dialog__header {
    padding: 16px 20px;
    border-bottom: 1px solid #ebeef5;
  }

  .el-dialog__body {
    padding: 0;
    height: 70vh;
    overflow: hidden;
  }

  .el-dialog__footer {
    padding: 16px 20px;
    border-top: 1px solid #ebeef5;
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  position: relative;

  .modal-title {
    font-size: 18px;
    font-weight: 600;
    color: #303133;
  }

  .modal-info {
    display: flex;
    align-items: center;
    gap: 16px;
    font-size: 14px;
    color: #606266;

    .image-position {
      font-weight: 500;
    }

    .image-scale {
      color: #909399;
    }
  }
}

/* 模态框选择按钮样式 - 与截图列表选择按钮保持一致 */
.modal-select-button {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  z-index: 5;
  cursor: pointer;
  background-color: rgba(64, 158, 255, 0.8);
  margin-right: 12px; /* 与位置信息保持适当间距 */
  flex-shrink: 0; /* 防止按钮被压缩 */

  &:hover {
    background-color: #409eff;
    transform: scale(1.1);
  }

  &.selected {
    background-color: rgba(103, 194, 58, 0.9);

    &:hover {
      background-color: #67c23a;
    }
  }
}

.modal-content {
  position: relative;
  height: 100%;
  display: flex;
  align-items: center;
  background: #f5f7fa;
}

.nav-button {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 48px;
  height: 48px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  color: #606266;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  z-index: 10;
  transition: all 0.3s ease;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

  &:hover:not(:disabled) {
    background: #409eff;
    color: white;
    transform: translateY(-50%) scale(1.1);
  }

  &:disabled {
    opacity: 0.3;
    cursor: not-allowed;
  }
}

.nav-button-left {
  left: 20px;
}

.nav-button-right {
  right: 20px;
}

.modal-image-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  overflow: hidden;
  cursor: pointer;
  padding: 0 80px;
}

.modal-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  transition: transform 0.3s ease;
  cursor: default;
  transform-origin: center;
}

.modal-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  .modal-controls-center {
    flex: 1;
    display: flex;
    justify-content: center;
  }

  .modal-controls-right {
    flex: 0 0 auto;
    display: flex;
    justify-content: flex-end;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .screenshot-container {
    gap: 8px;
  }

  .screenshot-wrapper {
    width: 120px;
    height: 80px;
  }

  .screenshot-title {
    font-size: 11px;
  }

  .screenshot-timestamp {
    font-size: 9px;
  }

  /* 移动端模态框优化 */
  .screenshot-modal {
    .el-dialog {
      max-width: 95vw;
      max-height: 95vh;
    }

    .modal-header {
      flex-direction: column;
      gap: 8px;
      align-items: flex-start;

      .modal-info {
        gap: 12px;
        font-size: 12px;
      }
    }

    .modal-content {
      .nav-button {
        width: 40px;
        height: 40px;
        font-size: 16px;
      }

      .nav-button-left {
        left: 10px;
      }

      .nav-button-right {
        right: 10px;
      }

      .modal-image-container {
        padding: 0 60px;
      }
    }

    .modal-footer {
      flex-direction: column;
      gap: 12px;

      .modal-controls-center,
      .modal-controls-right {
        flex: none;
        width: 100%;
        display: flex;
        justify-content: center;
      }
    }

    .modal-select-button {
      width: 20px;
      height: 20px;
      font-size: 12px;
      margin-right: 8px; /* 移动端减少间距 */
    }
  }
}

// 拖拽相关样式
.tree-node-wrapper {
  position: relative;

  &.drag-source {
    cursor: grab;

    &:hover {
      background-color: theme.$color-primary-lighter-45;
    }
  }

  &.dragging {
    opacity: 0.5;
    cursor: grabbing;
    background-color: theme.$color-primary-lighter-40;
    border: 2px dashed theme.$color-primary;
  }

  // 产品库拖拽样式（简化版）
  &.product-drag-source {
    cursor: grab;

    &:hover {
      background-color: theme.$color-primary-lighter-45;
      border: 1px solid rgba(theme.$color-primary, 0.3);
      border-radius: 4px;
    }
  }

  &.product-dragging {
    opacity: 0.6;
    cursor: grabbing;
    background-color: theme.$color-primary-lighter-35;
    border: 2px solid theme.$color-primary;
    transform: rotate(1deg);
    box-shadow: 0 4px 8px rgba(theme.$color-primary, 0.3);
    z-index: 1000;
  }

  &.drop-target {
    position: relative;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border: 2px dashed transparent;
      border-radius: 4px;
      pointer-events: none;
      transition: border-color 0.2s ease;
    }

    &.drag-over::after {
      border-color: theme.$color-primary;
      background-color: rgba(theme.$color-primary, 0.1);
    }

    // 产品库拖拽时的放置指示器
    &.drag-over {
      &::before {
        content: '';
        position: absolute;
        left: 0;
        right: 0;
        height: 2px;
        background-color: theme.$color-primary;
        z-index: 10;
        border-radius: 1px;
        box-shadow: 0 0 4px rgba(theme.$color-primary, 0.6);
        transition: all 0.2s ease;
      }
    }
  }
}

// 拖拽提示样式
.drag-hint {
  position: fixed;
  top: 10px;
  right: 10px;
  background: theme.$color-primary;
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 9999;
  box-shadow: theme.$shadow-soft;

  &.show {
    display: block;
  }

  &.hide {
    display: none;
  }
}

// 加载状态样式
.loading-state {
  padding: 20px;
  text-align: center;

  .loading-text {
    margin-top: 16px;
    color: theme.$color-text-secondary;
    font-size: 14px;
  }
}

// 表单操作按钮样式
.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-start;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid theme.$color-border;
}

// 脉冲动画
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}
</style>
