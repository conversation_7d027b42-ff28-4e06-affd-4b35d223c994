/**
 * API服务模块
 *
 * 提供统一的API接口调用服务，包括：
 * - 用户管理：登录、用户CRUD、权限管理
 * - 系统管理：系统CRUD、配置管理
 * - 浏览器管理：连接、断开、状态查询
 * - 录制管理：开始、暂停、停止、取消录制
 * - 用例管理：系统目录、用例数据
 * - 文件管理：上传、下载、删除
 *
 * <AUTHOR> Team
 * @version 2.0.0
 */

import axios from 'axios'
import type { AxiosInstance, AxiosResponse, AxiosRequestConfig } from 'axios'
import { ElMessage } from 'element-plus'
import { tokenManager } from '@/utils/tokenManager'
import type { CreateUserRequest } from '@/types/api/user'

// 扩展 Axios 类型以支持自定义请求头
declare module 'axios' {
  interface AxiosRequestConfig {
    _retry?: boolean
  }
}

// ================================
// 基础类型定义
// ================================

/**
 * API响应基础接口
 * 与后端保持一致的响应格式
 */
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  success: boolean
  timestamp?: string
  request_id?: string
}

/**
 * 增强的请求配置接口
 * 扩展AxiosRequestConfig，添加业务层面的配置选项
 */
export interface RequestConfig extends AxiosRequestConfig {
  // 错误处理配置
  showErrorMessage?: boolean      // 是否显示错误消息，默认true
  customErrorHandler?: (error: any) => void  // 自定义错误处理函数

  // 加载状态配置
  showLoading?: boolean          // 是否显示加载状态，默认false
  loadingMessage?: string        // 自定义加载消息

  // 重试配置
  retryCount?: number           // 重试次数，默认0
  retryDelay?: number          // 重试延迟(ms)，默认1000

  // 缓存配置
  useCache?: boolean           // 是否使用缓存，默认false
  cacheKey?: string           // 缓存键名
  cacheTTL?: number           // 缓存时间(ms)，默认5分钟

  // 响应处理配置
  extractData?: boolean        // 是否自动提取data字段，默认true
  validateResponse?: (data: any) => boolean  // 响应数据验证函数

  // 业务配置
  skipAuth?: boolean          // 是否跳过认证，默认false
  isolatedRequest?: boolean   // 是否为隔离请求，默认false
}

/**
 * 基础响应接口（无数据）
 */
export interface BaseResponse {
  code: number
  message: string
  success: boolean
  timestamp?: string
  request_id?: string
}

// ================================
// 用户管理相关类型
// ================================

/**
 * 登录请求参数
 */
export interface LoginRequest {
  username: string
  password: string
}

/**
 * 登录响应数据
 */
export interface LoginResponseData {
  access_token: string
  refresh_token?: string
  user: {
    id: string
    username: string
    role: string
  }
}

/**
 * 登录响应接口
 */
export interface LoginResponse extends ApiResponse<LoginResponseData> {}

/**
 * 用户数据接口（实际API格式）
 */
export interface UserData {
  _id: string  // 注意：实际API使用 _id 而不是 id
  username: string
  role: 'admin' | 'user'
  permissions: {
    write: Record<string, any>
    read: Record<string, any>
  }
  is_active: boolean
  created_at: string
  updated_at: string
  last_login: string | null
  avatar?: string  // 添加头像字段
  id?: string      // 添加id字段作为别名
}

/**
 * 用户响应接口
 */
export interface UserResponse extends ApiResponse<UserData> {}

/**
 * 用户列表数据
 */
export interface UserListData {
  users: UserData[]
  total: number
}

/**
 * 用户列表响应接口
 */
export interface UserListResponse extends ApiResponse<UserListData> {}

/**
 * 创建用户响应数据
 */
export interface CreateUserResponseData extends UserData {
  password: string  // API返回空字符串
}

/**
 * 创建用户响应接口
 */
export interface CreateUserResponse extends ApiResponse<CreateUserResponseData> {}

/**
 * 创建用户API响应（实际API格式）
 */
export interface CreateUserApiResponse extends UserManagementApiResponse<CreateUserResponseData> {}

/**
 * 用户管理API响应格式（实际API格式）
 */
export interface UserManagementApiResponse<T = any> {
  status: 'success' | 'error'
  message: string
  data: T
  timestamp: string
  request_id: string | null
}

/**
 * 用户列表API响应（实际API格式）
 */
export interface UserListApiResponse extends UserManagementApiResponse<UserListData> {
  users?: UserData[]  // 添加直接的users属性以支持不同的响应格式
  [key: string]: any  // 添加索引签名以支持动态属性访问
}

/**
 * 删除用户响应数据
 */
export interface DeleteUserResponseData {
  userId: string
}

/**
 * 删除用户API响应（实际API格式）
 */
export interface DeleteUserApiResponse extends UserManagementApiResponse<DeleteUserResponseData> {}

/**
 * 创建用户请求参数
 */
// CreateUserRequest 已在 types/api/user.ts 中定义

/**
 * 更新用户请求参数
 */
export interface UpdateUserRequest {
  username?: string
  role?: 'admin' | 'user'
  permissions?: {
    write: Record<string, any>
    read: Record<string, any>
  }
  is_active?: boolean
}

/**
 * 修改密码请求参数 - 新版本只需要新密码
 */
export interface ChangePasswordRequest {
  password: string
}

/**
 * 修改密码响应数据
 */
export interface ChangePasswordResponseData {
  userId: string
}

/**
 * 修改密码响应接口
 */
export interface ChangePasswordResponse extends UserManagementApiResponse<ChangePasswordResponseData> {}

// ================================
// 系统管理相关类型
// ================================

// 删除未使用的SystemData接口

// 删除未使用的SystemArrayResponse接口

// 删除未使用的SystemResponse接口



// ================================
// 用例管理相关类型
// ================================

/**
 * 系统配置项接口
 */
export interface SystemConfigItem {
  [configName: string]: TestItem[]
}

// 删除未使用的SystemPath接口

// 删除未使用的SystemDetail接口

// 删除未使用的SystemDetailResponse接口

/**
 * 创建系统请求接口
 */
export interface CreateSystemRequest {
  systemName: string
  subsystem?: string
  softwareName?: string
  softwareType?: string
  securityLevel?: string
  runtimeEnv?: string
  developEnv?: string
  language?: string
  version?: string
  codeTemplate?: string
  developer?: string
}

/**
 * 更新系统请求接口
 */
export interface UpdateSystemRequest extends Partial<CreateSystemRequest> {}

/**
 * 配置项请求接口
 */
export interface ConfigItemRequest {
  systemName: string
  configItemName: string
  configData?: Record<string, unknown>
}

/**
 * 创建配置项请求接口
 */
export interface CreateConfigItemRequest {
  systemName: string
  configItemName: string
  subsystem?: string
  softwareName?: string
  softwareType?: string
  securityLevel?: string
  runtimeEnv?: string
  developEnv?: string
  language?: string
  version?: string
  codeTemplate?: string
  developer?: string
}

/**
 * 设置默认用例路径请求接口
 */
export interface SetDefaultCasePathRequest {
  tableName: string
  testMethod: string
}

/**
 * 创建测试项请求接口
 */
export interface CreateTestItemRequest {
  systemName: string
  configItemName: string
  testItemName: string
}

/**
 * 更新配置项请求接口
 */
export interface UpdateConfigItemRequest {
  systemName: string
  configItemName: string
  newConfigItemName: string
  subsystem?: string
  softwareName?: string
  softwareType?: string
  securityLevel?: string
  runtimeEnv?: string
  developEnv?: string
  language?: string
  version?: string
  codeTemplate?: string
  developer?: string
}

/**
 * 用例选择请求接口
 */
export interface CaseSelectionRequest {
  systemName: string
  tableId: string
  caseId: string
  selected: boolean
}

/**
 * 用例顺序调整请求接口
 */
export interface CaseOrderRequest {
  caseListData: Record<string, any>
}

// ================================
// 权限管理相关类型
// ================================

/**
 * 权限数据接口
 */
export interface PermissionData {
  id: string
  name: string
  description?: string
  resource: string
  action: string
  created_at: string
  updated_at: string
}

/**
 * 权限响应接口
 */
export interface PermissionResponse extends ApiResponse<PermissionData> {}

/**
 * 权限列表响应接口
 */
export interface PermissionsResponse extends ApiResponse<PermissionData[]> {}



// ================================
// 浏览器管理相关类型
// ================================

/**
 * 浏览器状态数据接口
 */
export interface BrowserStatusData {
  status: string
  connected: boolean
  connection_state?: string
  remote_host?: string
  remote_port?: number
  pool_stats?: {
    active_connections: number
    total_connections: number
    total_users: number
  }
  user_pages_count?: number
  // 添加缺失的属性以支持不同的API响应格式
  is_connected?: boolean
  connection_detail?: string
}

/**
 * 浏览器状态响应接口
 */
export interface BrowserStatusResponse extends ApiResponse<BrowserStatusData> {
  status: 'success' | 'error'
  message: string
  // 添加直接属性支持，以兼容不同的响应格式
  is_connected?: boolean
  connection_detail?: string
  connected?: boolean
}

/**
 * 浏览器断开连接数据接口
 */
export interface BrowserDisconnectData {
  user_id: string
  username: string
  connection_state: string
}

/**
 * 浏览器断开连接响应接口
 */
export interface BrowserDisconnectResponse {
  status: string
  message: string
  data: BrowserDisconnectData
  timestamp: string
  request_id: string | null
}

// ================================
// 录制管理相关类型
// ================================

/**
 * 录制响应数据接口
 */
export interface RecordingResponseData {
  user_id: string
  file_path: string | null
}

/**
 * 录制基础响应接口
 */
export interface RecordingBaseResponse {
  status: string
  message: string
  success?: boolean
  data: RecordingResponseData
  timestamp: string
  request_id: string | null
}

/**
 * 开始录制响应接口
 */
export interface StartRecordingResponse extends RecordingBaseResponse {}

/**
 * 暂停录制响应接口
 */
export interface PauseRecordingResponse extends RecordingBaseResponse {}

/**
 * 继续录制响应接口
 */
export interface ResumeRecordingResponse extends RecordingBaseResponse {}

// 删除未使用的StopRecordingRequest接口

/**
 * 停止录制响应接口
 */
export interface StopRecordingResponse extends RecordingBaseResponse {}

/**
 * 取消录制响应接口
 */
export interface CancelRecordingResponse extends RecordingBaseResponse {}

// ================================
// 用例管理相关类型
// ================================

/**
 * 用例数据接口
 */
export interface CaseData {
  id: string
  selected: boolean
  deleted: boolean
}

/**
 * 测试项接口
 */
export interface TestItem {
  [testItemName: string]: CaseData[]
}

/**
 * 功能路径接口
 */
export interface FunctionPath {
  [functionName: string]: {
    tableName: string
    path: TestItem[]
  }
}

/**
 * 系统数据接口（用例管理）
 * 根据真实API响应数据结构定义
 */
export interface CaseSystemData {
  subsystem: string
  softwareName: string
  softwareType?: string
  securityLevel?: string
  runtimeEnv?: string
  developEnv?: string
  language?: string
  version?: string
  codeTemplate?: string
  developer?: string
  path: FunctionPath[]
}

/**
 * 系统原始响应接口（对象格式）
 */
export interface SystemsRawResponse {
  [systemKey: string]: CaseSystemData
}

/**
 * 系统响应接口（用例管理）
 */
export interface SystemsResponse extends ApiResponse<SystemsRawResponse> {}

/**
 * 前端树形节点接口
 */
export interface CaseNode {
  id: string
  name: string
  displayName: string
  type: 'system' | 'function' | 'testItem' | 'testCase'
  selected?: boolean
  deleted?: boolean
  children?: CaseNode[]
  tableName?: string
  originalName?: string
  systemKey?: string
  isExpandable?: boolean  // 标记节点是否可展开（支持添加子项）
  isEmpty?: boolean       // 标记节点是否为空目录
  canAcceptDrop?: boolean // 标记节点是否可接收拖拽
  // 添加缺失的属性以支持用例数据转换
  usecaseDetail?: any     // 用例详细信息
  originalData?: any      // 保存原始数据字段
}

/**
 * 转换后的系统数据接口
 */
export interface TransformedSystemsData {
  systems: CaseNode[]
  totalCases: number
  totalSystems: number
  productLibraryCases: number
  candidateLibraryCases: number
}

/**
 * 用例状态更新请求接口
 */
export interface UpdateCaseStatusRequest {
  caseId: string
  selected: boolean
  systemKey?: string
  functionName?: string
  testItemName?: string
}

// 删除未使用的UpdateCaseStatusResponse接口

/**
 * 批量用例状态更新请求接口
 */
export interface BatchUpdateCaseStatusRequest {
  updates: UpdateCaseStatusRequest[]
}

// 删除未使用的BatchUpdateCaseStatusResponse接口

// ================================
// 用例详情相关类型
// ================================

/**
 * 测试步骤接口
 */
export interface TestStep {
  stepNo: string
  inputAction: string
  expectedResult: string
  actualResult?: string
  evaluationCriteria?: string
  testConclusion?: string
}

/**
 * 版本信息接口 - 根据新的API响应结构
 */
export interface VersionInfo {
  _id: string
  savedBy: string
  savedAt: string
}

/**
 * 用例详情信息接口 - 根据实际API响应结构
 */
export interface CaseDetailInfo {
  _id: string
  caseName: string
  traceability: string
  summary: string
  initialization: string
  prerequisites: string
  testMethod: string
  terminationCriteria: string
  acceptanceCriteria: string
  executionStatus: string
  executionResult: string
  designer: string
  reviewer: string
  testTime: string | null
  issueId: string
  testSteps: TestStep[]
  savedBy: string
  savedAt: string
  sourceCaseLogId: string
  sourcePromptId: string
  generatedAt: string
  created_at: string
  updated_at: string
}

/**
 * 用例详情响应接口 - 根据实际API响应结构
 */
export interface CaseDetailResponse extends ApiResponse<{
  _id: string
  logId: {
    _id: string
    user_id: string
    record_count: number
    created_at: string
    operations: Array<{
      type: string
      text: string
      value: string
      index: number
      tag: string
      title: string
      placeholder: string
      page: {
        url: string
        title: string
      }
      timestamp: string
    }>
    updated_at: string
  }
  caseInfoId: CaseDetailInfo
  picIndexId: string
  versions: VersionInfo[]  // 更新为版本信息对象数组
  created_at: string
  updated_at: string
}> {}

/**
 * 保存用例请求接口
 */
export interface SaveCaseRequest {
  tableId: string  // 注意：使用 tableId 而不是 table_id
  caseData: CaseDetailInfo
}

/**
 * 创建用例请求接口
 */
export interface CreateCaseRequest {
  tableId: string
  caseName: string
  systemName: string
  testMethodName: string
  index: number
  selected: boolean
}

/**
 * 删除用例请求接口
 */
export interface DeleteCaseRequest {
  case_ids: string[]
}

/**
 * 恢复用例请求接口
 */
export interface RestoreCaseRequest {
  case_ids: string[]
}

/**
 * 移动用例到不同库的请求接口
 */
export interface MoveCaseBetweenLibrariesRequest {
  caseId: string
  fromLibrary: 'product' | 'candidate'
  toLibrary: 'product' | 'candidate'
}

/**
 * 用例库分类数据接口
 */
export interface CaseLibraryData {
  productLibrary: CaseNode[]
  candidateLibrary: CaseNode[]
  totalCases: number
  productLibraryCases: number
  candidateLibraryCases: number
}

// ================================
// 文件管理相关类型
// ================================

/**
 * 文件上传数据接口
 */
export interface FileUploadData {
  filename: string
  url: string
  size: number
  type: string
  upload_time: string
}

/**
 * 文件上传响应接口
 */
export interface FileUploadResponse extends ApiResponse<FileUploadData> {}

// ================================
// 提示词管理相关类型
// ================================

/**
 * 提示词项目接口
 */
export interface PromptItem {
  prompt_id: string
  title: string
  content: string
  description?: string
  category?: string
  status?: string
  tags?: string[]
  variables?: string[]
  created_by?: string
  created_at?: string
  updated_at?: string
  usage_count?: number
  is_public?: boolean
}

/**
 * 提示词列表数据
 */
export interface PromptsListData {
  prompts: PromptItem[]
  total: number
  page: number
  page_size: number
  total_pages: number
}

/**
 * 提示词创建请求
 */
export interface PromptCreateRequest {
  title: string
  content: string
  description?: string
  category?: string
  status?: string
  tags?: string[]
  is_public?: boolean
}

/**
 * 提示词创建响应
 */
export interface PromptCreateResponse {
  success: boolean
  message: string
  data: PromptItem
}

/**
 * 提示词删除响应
 */
export interface PromptDeleteResponse {
  message: string
}

// 删除未使用的PromptExecuteRequest接口

/**
 * 提示词执行响应
 */
export interface PromptExecuteResponse {
  success: boolean
  message: string
  data: {
    result: string
    execution_time: number
    token_usage?: {
      prompt_tokens: number
      completion_tokens: number
      total_tokens: number
    }
  }
}

/**
 * 提示词列表响应（兼容格式）
 */
export interface PromptsResponse extends ApiResponse<PromptsListData> {}

/**
 * 提示词详情响应（兼容格式）
 */
export interface PromptDetailResponse extends ApiResponse<PromptItem> {}

// ================================
// 会话管理相关类型
// ================================

/**
 * 会话数据接口
 */
export interface SessionData {
  id: string
  name: string
  status: string
  created_at: string
  updated_at: string
}

// 删除未使用的SessionListResponse接口

// 删除未使用的SessionDetailResponse接口

// ================================
// 用例管理相关类型（传统用例管理）
// ================================

/**
 * 用例节点接口
 */
export interface UsecaseNode {
  id: string
  name: string
  type: string
  parent_id?: string
  children?: UsecaseNode[]
  identifier?: string
  description?: string
  status?: string
  created_at?: string
  updated_at?: string
}

/**
 * 用例步骤接口
 */
export interface UsecaseStep {
  id: string
  sequence: number
  action: string
  input_data?: string
  expected_result: string
  actual_result?: string
  evaluation_criteria?: string
  test_conclusion?: string
  screenshot_url?: string
}

/**
 * 用例内容接口
 */
export interface UsecaseContent {
  id: string
  name: string
  identifier: string
  description?: string
  steps: UsecaseStep[]
  expected_results?: string
  test_data?: any
  status?: string
  version?: string
  created_at?: string
  updated_at?: string
}

// 删除未使用的UsecaseTreeResponse接口

// 删除未使用的UsecaseContentResponse接口

// 删除未使用的UsecaseListResponse接口

// ================================
// 报告管理相关类型
// ================================

/**
 * 报告数据接口
 */
export interface ReportData {
  id: string
  title: string
  status: string
  created_at: string
  updated_at?: string
  content?: string
  type?: string
}

/**
 * 系统信息接口（用于报告生成）
 */
export interface ReportSystemData {
  type: string
  subsystem: string
  softwareName: string
  softwareType: string
  securityLevel: string
  runtimeEnv: string
  developEnv: string
  language: string
  version: string
  codeTemplate: string
  developer: string
}

/**
 * 系统路径信息接口
 */
export interface ReportSystemPath {
  [configName: string]: {
    data: {
      type: string
      subsystem?: string
      softwareName?: string
      softwareType?: string
      securityLevel?: string
      runtimeEnv?: string
      developEnv?: string
      language?: string
      version?: string
      codeTemplate?: string
      developer?: string
    }
    path: Array<{
      [testMethodName: string]: {
        data: {
          type: string
        }
        path: Array<{
          [caseName: string]: {
            id: string
            selected: boolean
            deleted: boolean
          }
        }>
      }
    }>
    tableName?: string
  }
}

/**
 * 报告系统响应接口
 */
export interface ReportSystemsResponse {
  status: string
  message: string
  data: {
    [systemName: string]: {
      data: ReportSystemData
      path: ReportSystemPath[]
    }
  }
  timestamp: string
  request_id: string | null
}

/**
 * 生成报告请求接口
 */
export interface GenerateReportRequest {
  name: string
  type: string
}

/**
 * 生成报告响应接口
 */
export interface GenerateReportResponse {
  status: string
  message: string
  data: {
    export_id: string
    export_name: string
    export_type: string
    file_path: string
    file_name: string
    file_size_bytes: number
    test_cases_count: number
    export_time: string
    success: boolean
  }
  timestamp: string
  request_id: string | null
}

/**
 * 报告列表项接口
 */
export interface ReportListItem {
  export_id: string
  name: string
  type: string
  file_size: number
  status: string
  created_at: string
  user_id: string
  metadata: {
    test_cases_count: number
  }
}

/**
 * 报告列表响应接口
 */
export interface ReportListResponse {
  status: string
  message: string
  data: ReportListItem[]
  pagination: {
    page: number
    page_size: number
    total_items: number
    total_pages: number
    has_previous: boolean
    has_next: boolean
  }
  timestamp: string
  request_id: string | null
}

// 删除未使用的ReportListResponse接口

// ================================
// 统计数据相关类型
// ================================

/**
 * 统计数据接口
 */
export interface StatisticsData {
  [key: string]: any
}

// 删除未使用的StatisticsResponse接口






// API基础配置 - 使用空字符串让请求通过Vite代理
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || ''
const API_TIMEOUT = parseInt(import.meta.env.VITE_API_TIMEOUT || '30000', 10)

// 创建axios实例
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: API_TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
  // 启用请求和响应拦截器的错误重试
  validateStatus: (status) => status < 500, // 只有5xx错误才被认为是网络错误
})

// 获取token的统一方法
const getAuthToken = (): string | null => {
  return tokenManager.getAccessToken()
}

// 增强的请求拦截器 - 支持配置管理和智能认证
apiClient.interceptors.request.use(
  async (config) => {
    try {
      const globalConfig = requestConfigManager.getConfig()

      // 请求日志记录
      if (globalConfig.enableRequestLogging) {
        console.log('🚀 发起API请求:', {
          method: config.method?.toUpperCase(),
          url: config.url,
          baseURL: config.baseURL,
          fullURL: `${config.baseURL}${config.url}`,
          headers: config.headers
        })
      }

      // 检查是否跳过认证
      const skipAuth = (config as any).skipAuth || false

      if (!skipAuth) {
        // 智能获取token（如果需要会自动刷新）
        const tokenResult = await tokenManager.smartRefreshToken()

        if (tokenResult.success && tokenResult.accessToken) {
          config.headers[globalConfig.authHeaderName] = `Bearer ${tokenResult.accessToken}`
          if (globalConfig.enableRequestLogging) {
            console.log('🔑 使用新刷新的token')
          }
        } else if (tokenResult.message?.includes('Token仍然有效')) {
          // Token仍然有效，直接使用
          const token = getAuthToken()
          if (token) {
            config.headers[globalConfig.authHeaderName] = `Bearer ${token}`
            if (globalConfig.enableRequestLogging) {
              console.log('🔑 使用现有token')
            }
          }
        } else {
          if (globalConfig.enableRequestLogging) {
            console.log('⚠️ 无token，发送匿名请求')
          }
        }
      }

      // 添加请求标识，便于调试和跟踪
      const requestId = `req_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
      config.headers['X-Request-ID'] = requestId

      // 应用全局超时配置
      if (!config.timeout) {
        config.timeout = globalConfig.timeout
      }

      if (globalConfig.enableRequestLogging) {
        console.log('📤 最终请求配置:', {
          headers: config.headers,
          timeout: config.timeout,
          requestId
        })
      }

      return config
    } catch (_error) {
      const globalConfig = requestConfigManager.getConfig()
      if (globalConfig.enableRequestLogging) {
        console.error('❌ 请求拦截器错误:', _error)
      }
      // 即使token处理失败，也尝试继续请求（可能是公开API）
      return config
    }
  },
  (error) => {
    console.error('❌ 请求拦截器失败:', error)
    return Promise.reject(error)
  }
)

// 增强的响应拦截器 - 支持配置管理和智能错误处理
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    const globalConfig = requestConfigManager.getConfig()

    if (globalConfig.enableResponseLogging) {
      console.log('📥 收到API响应:', {
        status: response.status,
        statusText: response.statusText,
        url: response.config.url,
        requestId: response.config.headers?.['X-Request-ID'],
        dataType: typeof response.data,
        dataKeys: response.data && typeof response.data === 'object' ? Object.keys(response.data) : 'N/A'
      })
    }

    // 检查业务层面的错误
    if (response.data && typeof response.data === 'object') {
      const { success, code } = response.data

      // 如果后端返回了明确的失败状态
      if (success === false || (code && code !== 200 && code !== 0)) {
        console.warn('⚠️ 业务层面错误:', response.data)
        // 对于业务错误，不显示全局错误提示，让组件自行处理
        // ElMessage.error(message || '请求失败')
      } else {
        console.log('✅ 响应成功')
      }
    }

    return response
  },
  async (error) => {
    const originalRequest = error.config
    const requestId = originalRequest?.headers?.['X-Request-ID'] || 'unknown'

    console.error('❌ API请求失败:', {
      requestId,
      url: originalRequest?.url,
      method: originalRequest?.method,
      status: error.response?.status,
      statusText: error.response?.statusText,
      message: error.message,
      code: error.code,
      responseData: error.response?.data
    })

    // 处理401认证错误 - 自动刷新token并重试
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true

      try {
        const tokenResult = await tokenManager.smartRefreshToken()

        if (tokenResult.success && tokenResult.accessToken) {
          originalRequest.headers.Authorization = `Bearer ${tokenResult.accessToken}`
          return apiClient(originalRequest)
        } else {
          // Token刷新失败，清除认证信息并跳转登录
          tokenManager.clearTokens()
          if (typeof window !== 'undefined') {
            window.location.href = '/login'
          }
          return Promise.reject(new Error(tokenResult.message || 'Token刷新失败'))
        }
      } catch (refreshError) {
        tokenManager.clearTokens()
        if (typeof window !== 'undefined') {
          window.location.href = '/login'
        }
        return Promise.reject(refreshError)
      }
    }

    // 处理网络错误和其他HTTP错误
    let errorMessage = '请求失败'

    if (error.response) {
      // 服务器返回了错误状态码
      const { status, data } = error.response

      switch (status) {
        case 400:
          errorMessage = data?.message || '请求参数错误'
          break
        case 403:
          errorMessage = data?.message || '权限不足'
          break
        case 404:
          errorMessage = data?.message || '请求的资源不存在'
          break
        case 429:
          errorMessage = data?.message || '请求过于频繁，请稍后再试'
          break
        case 500:
          errorMessage = data?.message || '服务器内部错误'
          break
        case 502:
        case 503:
        case 504:
          errorMessage = '服务暂时不可用，请稍后再试'
          break
        default:
          errorMessage = data?.message || `请求失败 (${status})`
      }
    } else if (error.request) {
      // 网络错误
      errorMessage = '网络连接失败，请检查网络设置'
    } else {
      // 其他错误
      errorMessage = error.message || '未知错误'
    }

    // 显示错误提示（排除401错误，因为会自动处理）
    if (error.response?.status !== 401) {
      ElMessage.error(errorMessage)
    }

    return Promise.reject(error)
  }
)

// ================================
// 缓存管理辅助函数
// ================================

interface CacheItem {
  data: any
  timestamp: number
  ttl: number
}

const requestCache = new Map<string, CacheItem>()

/**
 * 获取缓存数据
 */
const getCachedData = (key: string): any | null => {
  const item = requestCache.get(key)
  if (!item) return null

  const now = Date.now()
  if (now - item.timestamp > item.ttl) {
    requestCache.delete(key)
    return null
  }

  return item.data
}

/**
 * 设置缓存数据
 */
const setCachedData = (key: string, data: any, ttl: number): void => {
  requestCache.set(key, {
    data,
    timestamp: Date.now(),
    ttl
  })
}

/**
 * 清除缓存
 */
const clearCache = (key?: string): void => {
  if (key) {
    requestCache.delete(key)
  } else {
    requestCache.clear()
  }
}

// 通用请求方法 - 提供类型安全的API调用，支持增强配置
const request = async <T = any>(config: RequestConfig): Promise<T> => {
  // 使用配置管理器合并配置
  const mergedConfig = requestConfigManager.mergeRequestConfig(config)

  // 提取增强配置选项
  const {
    showErrorMessage,
    customErrorHandler,
    showLoading,
    loadingMessage = '加载中...',
    retryCount,
    retryDelay,
    useCache,
    cacheKey,
    cacheTTL,
    extractData = true,
    validateResponse,
    skipAuth = false,
    isolatedRequest = false,
    ...axiosConfig
  } = mergedConfig

  // 生成请求ID用于性能监控
  const requestId = `req_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`

  // 开始性能监控
  performanceMonitor.startRequest(requestId, axiosConfig.url || '', axiosConfig.method || 'GET')

  // 缓存处理
  if (useCache && cacheKey) {
    const cachedData = getCachedData(cacheKey)
    if (cachedData) {
      console.log('🎯 使用缓存数据:', cacheKey)
      // 记录缓存命中的性能指标
      performanceMonitor.endRequest(requestId, 200, true, { cacheHit: true })
      return cachedData
    }
  }

  // 加载状态处理
  let loadingInstance: any = null
  if (showLoading) {
    // 这里可以集成Element Plus的Loading组件
    console.log('⏳', loadingMessage)
  }

  try {
    // 执行请求（支持重试）
    let lastError: any = null
    for (let attempt = 0; attempt <= (retryCount || 0); attempt++) {
      try {
        const response = await apiClient(axiosConfig)

        // 关闭加载状态
        if (loadingInstance) {
          loadingInstance.close()
        }

        // 响应数据处理
        let result = response.data

        // 如果响应数据包含标准的API响应格式，进行额外验证和数据提取
        if (result && typeof result === 'object' && extractData) {
          // 检查新的API响应格式: {status, message, data}
          if ('status' in result && 'data' in result) {
            const apiResponse = result as { status: string; message?: string; data: T }

            // 检查业务层面的成功状态
            if (apiResponse.status !== 'success') {
              throw new Error(apiResponse.message || '业务处理失败')
            }

            // 检查是否是创建类接口或报告类接口，如果是则返回完整响应
            const url = response.config?.url || ''
            const isCreateAPI = url.includes('/test-items') || url.includes('/cases') || url.includes('/create')
            const isReportAPI = url.includes('/export/') || url.includes('/case-management/systems')

            if (isCreateAPI || isReportAPI) {
              // 创建类接口和报告类接口返回完整响应数据
              result = apiResponse as any
            } else {
              // 其他接口返回实际的数据部分
              result = apiResponse.data
            }
          }
          // 检查旧的API响应格式: {success, message, data}
          else if ('success' in result) {
            const apiResponse = result as ApiResponse<T>

            // 检查业务层面的成功状态
            if (apiResponse.success === false) {
              throw new Error(apiResponse.message || '业务处理失败')
            }

            // 返回实际的数据部分
            result = apiResponse.data
          }
        }

        // 响应验证
        if (validateResponse && !validateResponse(result)) {
          throw new Error('响应数据验证失败')
        }

        // 缓存结果
        if (useCache && cacheKey) {
          setCachedData(cacheKey, result, cacheTTL || 5 * 60 * 1000)
        }

        // 记录成功的性能指标
        performanceMonitor.endRequest(requestId, response.status, true, {
          retryCount: attempt,
          responseSize: JSON.stringify(response.data).length,
          requestSize: axiosConfig.data ? JSON.stringify(axiosConfig.data).length : 0
        })

        return result
      } catch (error) {
        lastError = error

        // 如果还有重试机会，等待后重试
        if (attempt < (retryCount || 0)) {
          console.log(`🔄 请求失败，${retryDelay || 1000}ms后进行第${attempt + 1}次重试`)
          await new Promise(resolve => setTimeout(resolve, retryDelay || 1000))
          continue
        }

        // 所有重试都失败，抛出错误
        throw error
      }
    }

    throw lastError
  } catch (error: any) {
    // 关闭加载状态
    if (loadingInstance) {
      loadingInstance.close()
    }

    // 使用统一错误处理中心
    const standardError = errorHandler.handle(error, {
      showMessage: showErrorMessage,
      customHandler: customErrorHandler
    })

    // 记录失败的性能指标
    const status = error.response?.status || 0
    performanceMonitor.endRequest(requestId, status, false, {
      errorType: standardError.type,
      retryCount: retryCount || 0
    })

    throw standardError
  }
}

// ================================
// 专用请求函数
// ================================

/**
 * Blob数据请求函数（用于文件下载）
 */
const requestBlob = async (config: RequestConfig): Promise<Blob> => {
  const blobConfig: RequestConfig = {
    ...config,
    responseType: 'blob',
    extractData: false, // Blob请求不需要提取data字段
    showLoading: config.showLoading ?? true,
    loadingMessage: config.loadingMessage ?? '正在下载文件...'
  }

  const response = await request<Blob>(blobConfig)
  return response
}

/**
 * 文件上传请求函数
 */
const requestUpload = async (config: RequestConfig & {
  file: File | FormData,
  onUploadProgress?: (progressEvent: any) => void
}): Promise<any> => {
  const { file, onUploadProgress, ...restConfig } = config

  let formData: FormData
  if (file instanceof FormData) {
    formData = file
  } else {
    formData = new FormData()
    formData.append('file', file)
  }

  const uploadConfig: RequestConfig = {
    ...restConfig,
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
      ...restConfig.headers
    },
    onUploadProgress,
    showLoading: config.showLoading ?? true,
    loadingMessage: config.loadingMessage ?? '正在上传文件...'
  }

  return request(uploadConfig)
}

/**
 * 流式数据请求函数（用于大数据量传输）
 */
const requestStream = async (config: RequestConfig): Promise<ReadableStream> => {
  const streamConfig: RequestConfig = {
    ...config,
    responseType: 'stream',
    extractData: false,
    showLoading: config.showLoading ?? true,
    loadingMessage: config.loadingMessage ?? '正在加载数据流...'
  }

  return request<ReadableStream>(streamConfig)
}

/**
 * 隔离请求函数（不使用全局拦截器）
 */
const requestIsolated = async <T = any>(config: RequestConfig): Promise<T> => {
  // 创建独立的axios实例，不使用全局拦截器
  const isolatedClient = axios.create({
    baseURL: config.baseURL || apiClient.defaults.baseURL,
    timeout: config.timeout || apiClient.defaults.timeout
  })

  // 只添加基本的请求头
  const isolatedConfig: AxiosRequestConfig = {
    ...config,
    headers: {
      'Content-Type': 'application/json',
      ...config.headers
    }
  }

  try {
    const response = await isolatedClient(isolatedConfig)
    return response.data
  } catch (error: any) {
    if (config.customErrorHandler) {
      config.customErrorHandler(error)
    }
    throw error
  }
}

// ================================
// 统一错误处理中心
// ================================

/**
 * 错误类型枚举
 */
export enum ErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  AUTH_ERROR = 'AUTH_ERROR',
  PERMISSION_ERROR = 'PERMISSION_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  BUSINESS_ERROR = 'BUSINESS_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

/**
 * 标准化错误接口
 */
export interface StandardError {
  type: ErrorType
  code: string | number
  message: string
  details?: any
  timestamp: string
  requestId?: string
}

/**
 * 错误处理配置
 */
interface ErrorHandlerConfig {
  showMessage?: boolean
  logError?: boolean
  reportError?: boolean
  customHandler?: (error: StandardError) => void
}

/**
 * 统一错误处理器
 */
class ErrorHandler {
  private defaultConfig: ErrorHandlerConfig = {
    showMessage: true,
    logError: true,
    reportError: false
  }

  /**
   * 处理错误
   */
  handle(error: any, config: ErrorHandlerConfig = {}): StandardError {
    const finalConfig = { ...this.defaultConfig, ...config }
    const standardError = this.normalizeError(error)

    // 记录错误日志
    if (finalConfig.logError) {
      console.error('🚨 错误处理:', standardError)
    }

    // 显示错误消息
    if (finalConfig.showMessage) {
      this.showErrorMessage(standardError)
    }

    // 上报错误
    if (finalConfig.reportError) {
      this.reportError(standardError)
    }

    // 自定义处理
    if (finalConfig.customHandler) {
      finalConfig.customHandler(standardError)
    }

    return standardError
  }

  /**
   * 标准化错误对象
   */
  private normalizeError(error: any): StandardError {
    const timestamp = new Date().toISOString()

    // Axios错误
    if (error.isAxiosError) {
      const response = error.response
      const request = error.request

      if (response) {
        // 服务器响应错误
        const status = response.status
        const data = response.data

        if (status === 401) {
          return {
            type: ErrorType.AUTH_ERROR,
            code: status,
            message: '认证失败，请重新登录',
            details: data,
            timestamp,
            requestId: response.headers?.['x-request-id']
          }
        } else if (status === 403) {
          return {
            type: ErrorType.PERMISSION_ERROR,
            code: status,
            message: '权限不足，无法访问该资源',
            details: data,
            timestamp,
            requestId: response.headers?.['x-request-id']
          }
        } else if (status >= 400 && status < 500) {
          return {
            type: ErrorType.VALIDATION_ERROR,
            code: status,
            message: data?.message || '请求参数错误',
            details: data,
            timestamp,
            requestId: response.headers?.['x-request-id']
          }
        } else if (status >= 500) {
          return {
            type: ErrorType.BUSINESS_ERROR,
            code: status,
            message: data?.message || '服务器内部错误',
            details: data,
            timestamp,
            requestId: response.headers?.['x-request-id']
          }
        }
      } else if (request) {
        // 网络错误
        if (error.code === 'ECONNABORTED') {
          return {
            type: ErrorType.TIMEOUT_ERROR,
            code: 'TIMEOUT',
            message: '请求超时，请检查网络连接',
            details: error,
            timestamp
          }
        } else {
          return {
            type: ErrorType.NETWORK_ERROR,
            code: 'NETWORK',
            message: '网络连接失败，请检查网络设置',
            details: error,
            timestamp
          }
        }
      }
    }

    // 业务错误
    if (error instanceof Error) {
      return {
        type: ErrorType.BUSINESS_ERROR,
        code: 'BUSINESS',
        message: error.message,
        details: error,
        timestamp
      }
    }

    // 未知错误
    return {
      type: ErrorType.UNKNOWN_ERROR,
      code: 'UNKNOWN',
      message: '未知错误',
      details: error,
      timestamp
    }
  }

  /**
   * 显示错误消息
   */
  private showErrorMessage(error: StandardError): void {
    // 这里可以集成Element Plus的Message组件
    console.error('💬 错误消息:', error.message)

    // 根据错误类型显示不同的消息样式
    switch (error.type) {
      case ErrorType.AUTH_ERROR:
        // 显示警告样式的消息
        break
      case ErrorType.NETWORK_ERROR:
      case ErrorType.TIMEOUT_ERROR:
        // 显示网络错误样式的消息
        break
      default:
        // 显示默认错误样式的消息
        break
    }
  }

  /**
   * 上报错误
   */
  private reportError(error: StandardError): void {
    // 这里可以集成错误监控服务，如Sentry
    console.log('📊 错误上报:', error)
  }
}

// 创建全局错误处理器实例
export const errorHandler = new ErrorHandler()

// ================================
// 运行时类型验证系统
// ================================

/**
 * 基础类型验证器
 */
export class TypeValidator {
  /**
   * 验证对象是否符合指定的接口结构
   */
  static validateInterface<T>(data: any, schema: Record<string, any>): data is T {
    if (!data || typeof data !== 'object') {
      return false
    }

    for (const [key, validator] of Object.entries(schema)) {
      if (typeof validator === 'function') {
        if (!validator(data[key])) {
          console.warn(`类型验证失败: 字段 ${key} 不符合要求`)
          return false
        }
      } else if (typeof validator === 'object' && validator.required) {
        if (!(key in data)) {
          console.warn(`类型验证失败: 缺少必需字段 ${key}`)
          return false
        }
        if (validator.type && !validator.type(data[key])) {
          console.warn(`类型验证失败: 字段 ${key} 类型不正确`)
          return false
        }
      }
    }

    return true
  }

  /**
   * 验证API响应格式
   */
  static validateApiResponse(data: any): boolean {
    return this.validateInterface(data, {
      success: (value: any) => typeof value === 'boolean',
      message: (value: any) => typeof value === 'string',
      data: () => true // data字段可以是任意类型
    }) || this.validateInterface(data, {
      status: (value: any) => typeof value === 'string',
      message: (value: any) => typeof value === 'string',
      data: () => true
    })
  }

  /**
   * 验证用户数据
   */
  static validateUserData(data: any): boolean {
    return this.validateInterface(data, {
      _id: { required: true, type: (value: any) => typeof value === 'string' },
      username: { required: true, type: (value: any) => typeof value === 'string' },
      role: { required: true, type: (value: any) => ['admin', 'user'].includes(value) },
      is_active: { required: true, type: (value: any) => typeof value === 'boolean' }
    })
  }

  /**
   * 验证浏览器状态数据
   */
  static validateBrowserStatus(data: any): boolean {
    return this.validateInterface(data, {
      status: (value: any) => typeof value === 'string',
      connected: (value: any) => typeof value === 'boolean'
    })
  }

  /**
   * 验证用例数据
   */
  static validateCaseData(data: any): boolean {
    return this.validateInterface(data, {
      id: { required: true, type: (value: any) => typeof value === 'string' },
      name: { required: true, type: (value: any) => typeof value === 'string' },
      systemKey: (value: any) => typeof value === 'string'
    })
  }
}

/**
 * 常用验证器函数
 */
export const validators = {
  isString: (value: any): value is string => typeof value === 'string',
  isNumber: (value: any): value is number => typeof value === 'number',
  isBoolean: (value: any): value is boolean => typeof value === 'boolean',
  isArray: (value: any): value is any[] => Array.isArray(value),
  isObject: (value: any): value is object => value !== null && typeof value === 'object',
  isNonEmptyString: (value: any): value is string => typeof value === 'string' && value.length > 0,
  isValidEmail: (value: any): value is string => {
    if (typeof value !== 'string') return false
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(value)
  },
  isValidUrl: (value: any): value is string => {
    if (typeof value !== 'string') return false
    try {
      new URL(value)
      return true
    } catch {
      return false
    }
  }
}

// ================================
// 统一请求配置管理
// ================================

/**
 * 全局请求配置
 */
interface GlobalRequestConfig {
  // 基础配置
  baseURL: string
  timeout: number

  // 默认行为配置
  defaultShowLoading: boolean
  defaultShowErrorMessage: boolean
  defaultRetryCount: number
  defaultRetryDelay: number

  // 缓存配置
  defaultCacheTTL: number
  enableGlobalCache: boolean

  // 错误处理配置
  enableErrorReporting: boolean
  enableErrorLogging: boolean

  // 认证配置
  authTokenKey: string
  authHeaderName: string

  // 调试配置
  enableRequestLogging: boolean
  enableResponseLogging: boolean
}

/**
 * 请求配置管理器
 */
class RequestConfigManager {
  private config: GlobalRequestConfig = {
    baseURL: import.meta.env.VITE_API_BASE_URL || '',
    timeout: 30000,
    defaultShowLoading: false,
    defaultShowErrorMessage: true,
    defaultRetryCount: 0,
    defaultRetryDelay: 1000,
    defaultCacheTTL: 5 * 60 * 1000,
    enableGlobalCache: false,
    enableErrorReporting: false,
    enableErrorLogging: true,
    authTokenKey: 'access_token',
    authHeaderName: 'Authorization',
    enableRequestLogging: true,
    enableResponseLogging: true
  }

  /**
   * 获取全局配置
   */
  getConfig(): GlobalRequestConfig {
    return { ...this.config }
  }

  /**
   * 更新全局配置
   */
  updateConfig(updates: Partial<GlobalRequestConfig>): void {
    this.config = { ...this.config, ...updates }
    console.log('🔧 请求配置已更新:', updates)
  }

  /**
   * 重置为默认配置
   */
  resetConfig(): void {
    this.config = {
      baseURL: import.meta.env.VITE_API_BASE_URL || '',
      timeout: 30000,
      defaultShowLoading: false,
      defaultShowErrorMessage: true,
      defaultRetryCount: 0,
      defaultRetryDelay: 1000,
      defaultCacheTTL: 5 * 60 * 1000,
      enableGlobalCache: false,
      enableErrorReporting: false,
      enableErrorLogging: true,
      authTokenKey: 'access_token',
      authHeaderName: 'Authorization',
      enableRequestLogging: true,
      enableResponseLogging: true
    }
    console.log('🔄 请求配置已重置为默认值')
  }

  /**
   * 合并请求配置
   */
  mergeRequestConfig(requestConfig: RequestConfig): RequestConfig {
    return {
      baseURL: requestConfig.baseURL || this.config.baseURL,
      timeout: requestConfig.timeout || this.config.timeout,
      showLoading: requestConfig.showLoading ?? this.config.defaultShowLoading,
      showErrorMessage: requestConfig.showErrorMessage ?? this.config.defaultShowErrorMessage,
      retryCount: requestConfig.retryCount ?? this.config.defaultRetryCount,
      retryDelay: requestConfig.retryDelay ?? this.config.defaultRetryDelay,
      cacheTTL: requestConfig.cacheTTL ?? this.config.defaultCacheTTL,
      useCache: requestConfig.useCache ?? this.config.enableGlobalCache,
      ...requestConfig
    }
  }

  /**
   * 获取环境特定配置
   */
  getEnvironmentConfig(): Partial<GlobalRequestConfig> {
    const env = import.meta.env.MODE || 'development'

    switch (env) {
      case 'production':
        return {
          enableRequestLogging: false,
          enableResponseLogging: false,
          enableErrorReporting: true,
          timeout: 15000
        }
      case 'staging':
        return {
          enableRequestLogging: true,
          enableResponseLogging: false,
          enableErrorReporting: true,
          timeout: 20000
        }
      case 'development':
      default:
        return {
          enableRequestLogging: true,
          enableResponseLogging: true,
          enableErrorReporting: false,
          timeout: 30000
        }
    }
  }

  /**
   * 应用环境配置
   */
  applyEnvironmentConfig(): void {
    const envConfig = this.getEnvironmentConfig()
    this.updateConfig(envConfig)
  }
}

// 创建全局配置管理器实例
export const requestConfigManager = new RequestConfigManager()

// 应用环境特定配置
requestConfigManager.applyEnvironmentConfig()

// ================================
// 请求预设配置
// ================================

/**
 * 常用请求预设配置
 */
export const requestPresets = {
  // 快速请求（无加载状态，无重试）
  quick: (): Partial<RequestConfig> => ({
    showLoading: false,
    showErrorMessage: false,
    retryCount: 0,
    timeout: 5000
  }),

  // 静默请求（无任何UI反馈）
  silent: (): Partial<RequestConfig> => ({
    showLoading: false,
    showErrorMessage: false,
    retryCount: 0
  }),

  // 重要请求（显示加载，支持重试）
  important: (): Partial<RequestConfig> => ({
    showLoading: true,
    showErrorMessage: true,
    retryCount: 2,
    retryDelay: 1500
  }),

  // 文件操作请求
  file: (): Partial<RequestConfig> => ({
    showLoading: true,
    loadingMessage: '处理文件中...',
    timeout: 60000,
    retryCount: 1
  }),

  // 缓存请求
  cached: (cacheKey: string, ttl?: number): Partial<RequestConfig> => ({
    useCache: true,
    cacheKey,
    cacheTTL: ttl || 10 * 60 * 1000 // 默认10分钟
  }),

  // 实时数据请求
  realtime: (): Partial<RequestConfig> => ({
    useCache: false,
    timeout: 10000,
    retryCount: 1,
    retryDelay: 500
  })
}

// ================================
// 请求性能监控系统
// ================================

/**
 * 性能指标接口
 */
export interface PerformanceMetrics {
  requestId: string
  url: string
  method: string
  startTime: number
  endTime: number
  duration: number
  status: number
  success: boolean
  cacheHit: boolean
  retryCount: number
  errorType?: string
  responseSize?: number
  requestSize?: number
}

/**
 * 性能监控器
 */
class PerformanceMonitor {
  private metrics: Map<string, PerformanceMetrics> = new Map()
  private maxMetricsCount = 1000 // 最大保存的指标数量
  private enableMonitoring = true

  /**
   * 开始监控请求
   */
  startRequest(requestId: string, url: string, method: string): void {
    if (!this.enableMonitoring) return

    const metric: PerformanceMetrics = {
      requestId,
      url,
      method: method.toUpperCase(),
      startTime: performance.now(),
      endTime: 0,
      duration: 0,
      status: 0,
      success: false,
      cacheHit: false,
      retryCount: 0
    }

    this.metrics.set(requestId, metric)

    // 清理旧指标
    if (this.metrics.size > this.maxMetricsCount) {
      const oldestKey = this.metrics.keys().next().value
      if (oldestKey) {
        this.metrics.delete(oldestKey)
      }
    }
  }

  /**
   * 结束监控请求
   */
  endRequest(
    requestId: string,
    status: number,
    success: boolean,
    options: {
      cacheHit?: boolean
      retryCount?: number
      errorType?: string
      responseSize?: number
      requestSize?: number
    } = {}
  ): void {
    if (!this.enableMonitoring) return

    const metric = this.metrics.get(requestId)
    if (!metric) return

    const endTime = performance.now()
    metric.endTime = endTime
    metric.duration = endTime - metric.startTime
    metric.status = status
    metric.success = success
    metric.cacheHit = options.cacheHit || false
    metric.retryCount = options.retryCount || 0
    metric.errorType = options.errorType
    metric.responseSize = options.responseSize
    metric.requestSize = options.requestSize

    // 记录性能日志
    this.logPerformance(metric)
  }

  /**
   * 记录性能日志
   */
  private logPerformance(metric: PerformanceMetrics): void {
    const { requestId, url, method, duration, status, success, cacheHit, retryCount } = metric

    const logData = {
      requestId,
      url,
      method,
      duration: `${duration.toFixed(2)}ms`,
      status,
      success,
      cacheHit,
      retryCount
    }

    if (success) {
      if (duration > 3000) {
        console.warn('🐌 慢请求警告:', logData)
      } else if (cacheHit) {
        console.log('⚡ 缓存命中:', logData)
      } else {
        console.log('📊 请求完成:', logData)
      }
    } else {
      console.error('❌ 请求失败:', { ...logData, errorType: metric.errorType })
    }
  }

  /**
   * 获取性能统计
   */
  getStatistics(): {
    totalRequests: number
    successRate: number
    averageDuration: number
    cacheHitRate: number
    slowRequestCount: number
    errorsByType: Record<string, number>
  } {
    const metrics = Array.from(this.metrics.values())
    const totalRequests = metrics.length

    if (totalRequests === 0) {
      return {
        totalRequests: 0,
        successRate: 0,
        averageDuration: 0,
        cacheHitRate: 0,
        slowRequestCount: 0,
        errorsByType: {}
      }
    }

    const successfulRequests = metrics.filter(m => m.success).length
    const cacheHits = metrics.filter(m => m.cacheHit).length
    const slowRequests = metrics.filter(m => m.duration > 3000).length
    const totalDuration = metrics.reduce((sum, m) => sum + m.duration, 0)

    const errorsByType: Record<string, number> = {}
    metrics.filter(m => !m.success && m.errorType).forEach(m => {
      errorsByType[m.errorType!] = (errorsByType[m.errorType!] || 0) + 1
    })

    return {
      totalRequests,
      successRate: (successfulRequests / totalRequests) * 100,
      averageDuration: totalDuration / totalRequests,
      cacheHitRate: (cacheHits / totalRequests) * 100,
      slowRequestCount: slowRequests,
      errorsByType
    }
  }

  /**
   * 获取最近的请求指标
   */
  getRecentMetrics(count: number = 10): PerformanceMetrics[] {
    return Array.from(this.metrics.values())
      .sort((a, b) => b.startTime - a.startTime)
      .slice(0, count)
  }

  /**
   * 清空指标
   */
  clearMetrics(): void {
    this.metrics.clear()
  }

  /**
   * 启用/禁用监控
   */
  setMonitoring(enabled: boolean): void {
    this.enableMonitoring = enabled
  }

  /**
   * 导出性能报告
   */
  exportReport(): string {
    const stats = this.getStatistics()
    const recentMetrics = this.getRecentMetrics(20)

    return JSON.stringify({
      timestamp: new Date().toISOString(),
      statistics: stats,
      recentRequests: recentMetrics
    }, null, 2)
  }
}

// 创建全局性能监控器实例
export const performanceMonitor = new PerformanceMonitor()

// ================================
// API调用统计和监控面板
// ================================

/**
 * API监控面板数据接口
 */
export interface MonitoringDashboard {
  overview: {
    totalRequests: number
    successRate: number
    averageResponseTime: number
    cacheHitRate: number
    activeRequests: number
  }
  performance: {
    slowRequests: PerformanceMetrics[]
    fastestRequests: PerformanceMetrics[]
    recentErrors: PerformanceMetrics[]
  }
  statistics: {
    requestsByMethod: Record<string, number>
    requestsByStatus: Record<string, number>
    errorsByType: Record<string, number>
    hourlyRequestCount: Array<{ hour: string; count: number }>
  }
}

/**
 * API监控管理器
 */
class ApiMonitoringManager {
  private activeRequests = new Set<string>()
  private requestHistory: PerformanceMetrics[] = []
  private maxHistorySize = 5000

  /**
   * 记录请求开始
   */
  recordRequestStart(requestId: string): void {
    this.activeRequests.add(requestId)
  }

  /**
   * 记录请求结束
   */
  recordRequestEnd(requestId: string, metrics: PerformanceMetrics): void {
    this.activeRequests.delete(requestId)

    // 添加到历史记录
    this.requestHistory.push(metrics)

    // 限制历史记录大小
    if (this.requestHistory.length > this.maxHistorySize) {
      this.requestHistory = this.requestHistory.slice(-this.maxHistorySize)
    }
  }

  /**
   * 获取监控面板数据
   */
  getDashboardData(): MonitoringDashboard {
    const stats = performanceMonitor.getStatistics()
    const recentMetrics = this.requestHistory.slice(-100)

    // 计算概览数据
    const overview = {
      totalRequests: stats.totalRequests,
      successRate: stats.successRate,
      averageResponseTime: stats.averageDuration,
      cacheHitRate: stats.cacheHitRate,
      activeRequests: this.activeRequests.size
    }

    // 性能分析
    const sortedByDuration = [...recentMetrics].sort((a, b) => a.duration - b.duration)
    const performanceData = {
      slowRequests: sortedByDuration.slice(-10).reverse(), // 最慢的10个请求
      fastestRequests: sortedByDuration.slice(0, 10), // 最快的10个请求
      recentErrors: recentMetrics.filter(m => !m.success).slice(-10) // 最近的10个错误
    }

    // 统计数据
    const requestsByMethod: Record<string, number> = {}
    const requestsByStatus: Record<string, number> = {}
    const hourlyRequestCount: Array<{ hour: string; count: number }> = []

    recentMetrics.forEach(metric => {
      // 按方法统计
      requestsByMethod[metric.method] = (requestsByMethod[metric.method] || 0) + 1

      // 按状态统计
      const statusGroup = Math.floor(metric.status / 100) * 100
      const statusKey = `${statusGroup}xx`
      requestsByStatus[statusKey] = (requestsByStatus[statusKey] || 0) + 1
    })

    // 按小时统计（最近24小时）
    const now = new Date()
    for (let i = 23; i >= 0; i--) {
      const hour = new Date(now.getTime() - i * 60 * 60 * 1000)
      const hourKey = hour.getHours().toString().padStart(2, '0') + ':00'
      const hourStart = hour.setMinutes(0, 0, 0)
      const hourEnd = hourStart + 60 * 60 * 1000

      const count = recentMetrics.filter(m => {
        const requestTime = m.startTime + (performance.timeOrigin || 0)
        return requestTime >= hourStart && requestTime < hourEnd
      }).length

      hourlyRequestCount.push({ hour: hourKey, count })
    }

    const statistics = {
      requestsByMethod,
      requestsByStatus,
      errorsByType: stats.errorsByType,
      hourlyRequestCount
    }

    return {
      overview,
      performance: performanceData,
      statistics
    }
  }

  /**
   * 获取实时状态
   */
  getRealTimeStatus(): {
    activeRequests: number
    requestsPerMinute: number
    averageResponseTime: number
    errorRate: number
  } {
    const recentMetrics = this.requestHistory.slice(-60) // 最近60个请求
    const now = Date.now()
    const oneMinuteAgo = now - 60 * 1000

    const recentRequestsInMinute = recentMetrics.filter(m =>
      (m.startTime + (performance.timeOrigin || 0)) > oneMinuteAgo
    )

    const totalDuration = recentRequestsInMinute.reduce((sum, m) => sum + m.duration, 0)
    const errorCount = recentRequestsInMinute.filter(m => !m.success).length

    return {
      activeRequests: this.activeRequests.size,
      requestsPerMinute: recentRequestsInMinute.length,
      averageResponseTime: recentRequestsInMinute.length > 0 ? totalDuration / recentRequestsInMinute.length : 0,
      errorRate: recentRequestsInMinute.length > 0 ? (errorCount / recentRequestsInMinute.length) * 100 : 0
    }
  }

  /**
   * 导出监控报告
   */
  exportMonitoringReport(): string {
    const dashboardData = this.getDashboardData()
    const realTimeStatus = this.getRealTimeStatus()

    return JSON.stringify({
      timestamp: new Date().toISOString(),
      realTimeStatus,
      dashboard: dashboardData,
      systemInfo: {
        userAgent: navigator.userAgent,
        platform: navigator.platform,
        language: navigator.language
      }
    }, null, 2)
  }

  /**
   * 清空监控数据
   */
  clearMonitoringData(): void {
    this.requestHistory = []
    this.activeRequests.clear()
    performanceMonitor.clearMetrics()
  }
}

// 创建全局API监控管理器实例
export const apiMonitoringManager = new ApiMonitoringManager()

// 删除未使用的httpMethods常量

// 完整的 apiService 导出
export const apiService = {
  // 认证相关
  auth: {
    login: async (credentials: LoginRequest): Promise<LoginResponse> => {
      return request<LoginResponse>({
        method: 'POST',
        url: '/api/v1/auth/login',
        data: credentials
      })
    },

    logout: async (): Promise<BaseResponse> => {
      return request<BaseResponse>({
        method: 'POST',
        url: '/api/v1/auth/logout'
      })
    },

    profile: async (): Promise<ApiResponse<UserData>> => {
      return request<ApiResponse<UserData>>({
        method: 'GET',
        url: '/api/v1/auth/profile'
      })
    },

    refreshToken: async (): Promise<ApiResponse<{ access_token: string; refresh_token?: string }>> => {
      const refreshToken = tokenManager.getRefreshToken()
      return request<ApiResponse<{ access_token: string; refresh_token?: string }>>({
        method: 'POST',
        url: '/api/v1/auth/refresh',
        data: { refresh_token: refreshToken }
      })
    },

    // 修改密码
    changePassword: async (passwordData: ChangePasswordRequest): Promise<ChangePasswordResponse> => {
      return request<ChangePasswordResponse>({
        method: 'PUT',
        url: '/api/v1/multi-user/users/password',
        data: passwordData
      })
    }
  },

  // 用户管理 - 添加所有缺失的方法
  users: {
    // 登录
    login: async (credentials: LoginRequest): Promise<LoginResponse> => {
      return request<LoginResponse>({
        method: 'POST',
        url: '/api/v1/multi-user/login',
        data: credentials
      })
    },

    // 登出
    logout: async (): Promise<BaseResponse> => {
      return request<BaseResponse>({
        method: 'POST',
        url: '/api/v1/multi-user/logout'
      })
    },

    // 删除未使用的getCurrentUser方法

    // 获取用户列表（管理员功能）
    list: async (params?: { page?: number; page_size?: number }): Promise<UserListApiResponse> => {
      return request<UserListApiResponse>({
        method: 'GET',
        url: '/api/v1/multi-user/admin/users',
        params
      })
    },

    // 创建用户（管理员功能）
    create: async (userData: CreateUserRequest): Promise<CreateUserApiResponse> => {
      return request<CreateUserApiResponse>({
        method: 'POST',
        url: '/api/v1/multi-user/admin/users/create',
        data: userData
      })
    },

    // 更新用户（管理员功能）
    update: async (userId: string, userData: UpdateUserRequest): Promise<UserResponse> => {
      return request<UserResponse>({
        method: 'PUT',
        url: `/api/v1/multi-user/admin/users/${userId}`,
        data: userData
      })
    },

    // 删除用户（管理员功能）
    delete: async (userId: string): Promise<DeleteUserApiResponse> => {
      return request<DeleteUserApiResponse>({
        method: 'DELETE',
        url: `/api/v1/multi-user/admin/users/${userId}/delete`
      })
    }
  },



  // 权限管理
  permissions: {
    // 获取权限列表
    list: async (): Promise<PermissionsResponse> => {
      return request<PermissionsResponse>({
        method: 'GET',
        url: '/api/v1/permissions'
      })
    },

    // 创建权限
    create: async (permissionData: Partial<PermissionData>): Promise<PermissionResponse> => {
      return request<PermissionResponse>({
        method: 'POST',
        url: '/api/v1/permissions',
        data: permissionData
      })
    },

    // 更新权限
    update: async (permissionId: string, permissionData: Partial<PermissionData>): Promise<PermissionResponse> => {
      return request<PermissionResponse>({
        method: 'PUT',
        url: `/api/v1/permissions/${permissionId}`,
        data: permissionData
      })
    },

    // 删除权限
    delete: async (permissionId: string): Promise<BaseResponse> => {
      return request<BaseResponse>({
        method: 'DELETE',
        url: `/api/v1/permissions/${permissionId}`
      })
    },

    // 获取用户权限
    getUserPermissions: async (userId: string): Promise<PermissionsResponse> => {
      return request<PermissionsResponse>({
        method: 'GET',
        url: `/api/v1/permissions/user-permissions/${userId}`
      })
    },

    // 更新用户权限
    updateUserPermissions: async (userId: string, permissions: PermissionData[]): Promise<BaseResponse> => {
      return request<BaseResponse>({
        method: 'PUT',
        url: `/api/v1/permissions/user-permissions/${userId}`,
        data: { permissions }
      })
    },

    checkPermission: async (permission: string): Promise<ApiResponse<{ hasPermission: boolean }>> => {
      return request<ApiResponse<{ hasPermission: boolean }>>({
        method: 'GET',
        url: `/api/v1/permissions/check/${permission}`
      })
    },

    // 注意：权限矩阵数据现在从用户权限数据中提取，不再需要单独的权限矩阵接口
  },

  // 权限管理（兼容旧接口名称 - 单数形式）
  permission: {
    // 用户管理方法（权限管理界面专用）
    getUsers: async (): Promise<UserListResponse> => {
      // 调用新的API并转换为旧格式
      const response = await request<UserListApiResponse>({
        method: 'GET',
        url: '/api/v1/multi-user/admin/users'
      })

      // request函数现在会自动提取data字段，所以response应该直接是用户数据
      let usersArray: any[] = [];

      if (Array.isArray(response)) {
        usersArray = response;
      } else if (response && (response as any).users && Array.isArray((response as any).users)) {
        usersArray = (response as any).users;
      } else if (response && response.data && Array.isArray(response.data)) {
        usersArray = response.data;
      } else {
        console.error('❌ getUsers: 无法识别的响应格式:', response);
        usersArray = [];
      }

      // 转换用户数据格式：_id -> id，并保持完整的UserData结构
      const convertedUsers: UserData[] = usersArray.map(user => ({
        _id: user._id,
        username: user.username,
        role: user.role,
        permissions: user.permissions,
        is_active: user.is_active,
        created_at: user.created_at,
        updated_at: user.updated_at,
        last_login: user.last_login
      }))

      // 包装为旧的ApiResponse格式以保持兼容性
      return {
        code: 200,
        message: 'success',
        data: {
          users: convertedUsers,
          total: convertedUsers.length,
          page: 1,
          page_size: convertedUsers.length
        },
        success: true
      } as UserListResponse
    },

    // 创建用户（兼容方法）
    createUser: async (userData: CreateUserRequest): Promise<CreateUserResponse> => {
      const response = await request<CreateUserApiResponse>({
        method: 'POST',
        url: '/api/v1/multi-user/admin/users/create',
        data: userData
      })

      // 转换响应格式
      return {
        code: 200,
        message: response.message,
        data: {
          _id: response.data._id,
          username: response.data.username,
          role: response.data.role,
          permissions: response.data.permissions,
          is_active: response.data.is_active,
          created_at: response.data.created_at,
          updated_at: response.data.updated_at,
          last_login: response.data.last_login,
          password: response.data.password
        },
        success: response.status === 'success'
      } as CreateUserResponse
    },

    // 删除用户（兼容方法）
    deleteUser: async (userId: string): Promise<BaseResponse> => {
      const response = await request<DeleteUserApiResponse>({
        method: 'DELETE',
        url: `/api/v1/multi-user/admin/users/${userId}/delete`
      })

      // 包装为旧的BaseResponse格式以保持兼容性
      return {
        code: 200,
        message: response.message,
        data: response.data,
        success: response.status === 'success'
      } as BaseResponse
    },

    // 修改用户密码（管理员功能）
    changePassword: async (userId: string, data: { new_password: string; admin_password: string }): Promise<any> => {
      return request<any>({
        method: 'POST',
        url: `/api/v1/multi-user/admin/users/${userId}/change-password`,
        data,
        extractData: false // 禁用自动数据提取，保持完整响应
      })
    },

    // 注意：系统数据现在从用户权限数据中提取，不再需要单独的系统接口

    // 注意：系统管理和权限矩阵相关接口已移除，因为后端不存在这些接口
    // 所有数据现在从用户权限数据中提取

    // 设置用户权限（多用户系统）
    setUserPermissions: async (userId: string, permissions: { read: Record<string, Record<string, any>>; write: Record<string, Record<string, any>> }): Promise<BaseResponse> => {
      return request<BaseResponse>({
        method: 'POST',
        url: '/api/v1/multi-user/permissions/set',
        data: {
          userId,
          permissions
        }
      })
    },

    // 获取多用户系统的用户列表（权限管理专用）
    getMultiUserAdminUsers: async (params?: { page?: number; page_size?: number }): Promise<UserListApiResponse> => {
      return request<UserListApiResponse>({
        method: 'GET',
        url: '/api/v1/multi-user/admin/users',
        params: params || { page: 1, page_size: 100 }
      })
    },

    // 更新用户权限（兼容方法）
    updateUserPermissions: async (userId: string, permissions: PermissionData[]): Promise<BaseResponse> => {
      return request<BaseResponse>({
        method: 'PUT',
        url: `/api/v1/permissions/user-permissions/${userId}`,
        data: { permissions }
      })
    },


  },



  // 注意：系统管理接口已移除，因为后端不存在这些接口

  // 提示词管理（使用新的RESTful API路径）
  prompts: {
    // 获取提示词列表 - GET: /api/v1/prompts/
    list: async (params?: { search?: string; page?: number; page_size?: number }): Promise<PromptsListData> => {
      return request<PromptsListData>({
        method: 'GET',
        url: '/api/v1/prompts/',
        params
      })
    },

    // 获取提示词详情 - GET: /api/v1/prompts/{prompt_id}
    get: async (promptId: string): Promise<PromptItem> => {
      return request<PromptItem>({
        method: 'GET',
        url: `/api/v1/prompts/${promptId}`
      })
    },

    // 创建提示词 - POST: /api/v1/prompts/
    create: async (promptData: PromptCreateRequest): Promise<PromptCreateResponse> => {
      return request<PromptCreateResponse>({
        method: 'POST',
        url: '/api/v1/prompts/',
        data: promptData
      })
    },

    // 更新提示词 - PUT: /api/v1/prompts/{prompt_id}
    update: async (promptId: string, promptData: PromptCreateRequest): Promise<PromptItem> => {
      return request<PromptItem>({
        method: 'PUT',
        url: `/api/v1/prompts/${promptId}`,
        data: promptData
      })
    },

    // 删除提示词 - DELETE: /api/v1/prompts/{prompt_id}
    delete: async (promptId: string): Promise<PromptDeleteResponse> => {
      return request<PromptDeleteResponse>({
        method: 'DELETE',
        url: `/api/v1/prompts/${promptId}`
      })
    },

    // 执行提示词 - POST: /api/v1/prompts/{prompt_id}/execute
    execute: async (promptId: string, executeData?: { variables?: Record<string, any>; context?: string }): Promise<PromptExecuteResponse> => {
      return request<PromptExecuteResponse>({
        method: 'POST',
        url: `/api/v1/prompts/${promptId}/execute`,
        data: executeData || {}
      })
    },

    // 兼容旧方法名称（使用新的RESTful API路径）
    getPrompts: async (params?: { page?: number; page_size?: number; category?: string; search?: string }): Promise<PromptsResponse> => {
      // 调用新的list方法并包装为旧格式
      const response = await request<PromptsListData>({
        method: 'GET',
        url: '/api/v1/prompts/',
        params: { search: params?.search, page: params?.page, page_size: params?.page_size }
      })

      // 包装为旧的ApiResponse格式以保持兼容性
      return {
        code: 200,
        message: 'success',
        data: response,
        success: true
      } as PromptsResponse
    },

    getPrompt: async (promptId: string): Promise<PromptDetailResponse> => {
      // 调用新的get方法并包装为旧格式
      const response = await request<PromptItem>({
        method: 'GET',
        url: `/api/v1/prompts/${promptId}`
      })

      // 包装为旧的ApiResponse格式以保持兼容性
      return {
        code: 200,
        message: 'success',
        data: response,
        success: true
      } as PromptDetailResponse
    },

    createPrompt: async (promptData: Omit<PromptItem, 'prompt_id' | 'created_at' | 'updated_at'>): Promise<PromptDetailResponse> => {
      // 转换数据格式并调用新的create方法
      const createRequest: PromptCreateRequest = {
        title: promptData.title,
        content: promptData.content,
        description: promptData.description,
        category: promptData.category,
        status: promptData.status,
        tags: promptData.tags,
        is_public: promptData.is_public
      }

      const response = await request<PromptCreateResponse>({
        method: 'POST',
        url: '/api/v1/prompts/',
        data: createRequest
      })

      // 包装为旧的ApiResponse格式以保持兼容性
      return {
        code: 200,
        message: response.message,
        data: response.data,
        success: response.success
      } as PromptDetailResponse
    },

    updatePrompt: async (promptId: string, promptData: Partial<PromptItem>): Promise<PromptDetailResponse> => {
      // 转换数据格式并调用新的update方法
      const updateRequest: PromptCreateRequest = {
        title: promptData.title || '',
        content: promptData.content || '',
        description: promptData.description,
        category: promptData.category,
        status: promptData.status,
        tags: promptData.tags,
        is_public: promptData.is_public
      }

      const response = await request<PromptItem>({
        method: 'PUT',
        url: `/api/v1/prompts/${promptId}`,
        data: updateRequest
      })

      // 包装为旧的ApiResponse格式以保持兼容性
      return {
        code: 200,
        message: 'success',
        data: response,
        success: true
      } as PromptDetailResponse
    },

    // 删除提示词（兼容旧方法名）
    deletePrompt: async (promptId: string): Promise<BaseResponse> => {
      const response = await request<PromptDeleteResponse>({
        method: 'DELETE',
        url: `/api/v1/prompts/${promptId}`
      })

      // 包装为旧的BaseResponse格式以保持兼容性
      return {
        code: 200,
        message: response.message,
        data: null,
        success: true
      } as BaseResponse
    },

    // 执行提示词（兼容方法名）
    executePrompt: async (promptId: string, executeData?: { variables?: Record<string, any>; context?: string }): Promise<PromptExecuteResponse> => {
      return request<PromptExecuteResponse>({
        method: 'POST',
        url: `/api/v1/prompts/${promptId}/execute`,
        data: executeData || {}
      })
    },

    // 设置活跃提示词 - POST: /api/v1/prompts/generate-test-cases
    setActivePrompt: async (promptId: string): Promise<{ status: string; message: string; data: { prompt_id: string; prompt_title: string } }> => {
      try {
        const response = await axios({
          method: 'POST',
          url: '/api/v1/prompts/generate-test-cases',
          data: { prompt_id: promptId },
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${getAuthToken()}`
          }
        })

        console.log('🔗 设置活跃提示词原始响应:', response)
        console.log('🔗 响应数据:', response.data)

        // 直接返回响应数据，保持完整的API响应结构
        return response.data
      } catch (error: any) {
        console.error('❌ 设置活跃提示词API调用失败:', error)

        // 如果是HTTP错误响应，尝试提取错误信息
        if (error.response?.data) {
          throw new Error(error.response.data.message || '设置活跃提示词失败')
        }

        throw error
      }
    },

    // 获取活跃提示词 - GET: /api/v1/prompts/generate-test-cases
    getActivePrompt: async (): Promise<{ status: string; message: string; data: { prompt_id: string; prompt_title: string } }> => {
      try {
        const response = await axios({
          method: 'GET',
          url: '/api/v1/prompts/generate-test-cases',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${getAuthToken()}`
          }
        })

        console.log('🔗 获取活跃提示词原始响应:', response)
        console.log('🔗 响应数据:', response.data)

        // 直接返回响应数据，保持完整的API响应结构
        return response.data
      } catch (error: any) {
        console.error('❌ 获取活跃提示词API调用失败:', error)

        // 如果是HTTP错误响应，尝试提取错误信息
        if (error.response?.data) {
          throw new Error(error.response.data.message || '获取活跃提示词失败')
        }

        throw error
      }
    }
  },

  // AI配置
  ai: {
    config: {
      get: async (): Promise<ApiResponse<any>> => {
        return request<ApiResponse<any>>({
          method: 'GET',
          url: '/api/v1/ai/config'
        })
      },

      update: async (config: any): Promise<BaseResponse> => {
        return request<BaseResponse>({
          method: 'PUT',
          url: '/api/v1/ai/config',
          data: config
        })
      }
    }
  },

  // 会话管理
  sessions: {
    list: async (): Promise<ApiResponse<SessionData[]>> => {
      return request<ApiResponse<SessionData[]>>({
        method: 'GET',
        url: '/api/v1/sessions'
      })
    },

    get: async (sessionId: string): Promise<ApiResponse<SessionData>> => {
      return request<ApiResponse<SessionData>>({
        method: 'GET',
        url: `/api/v1/sessions/${sessionId}`
      })
    },

    create: async (sessionData: { name: string }): Promise<ApiResponse<SessionData>> => {
      return request<ApiResponse<SessionData>>({
        method: 'POST',
        url: '/api/v1/sessions',
        data: sessionData
      })
    },

    delete: async (sessionId: string): Promise<BaseResponse> => {
      return request<BaseResponse>({
        method: 'DELETE',
        url: `/api/v1/sessions/${sessionId}`
      })
    }
  },

  // 用例管理
  usecases: {
    // 获取用例树结构
    tree: async (): Promise<ApiResponse<UsecaseNode[]>> => {
      return request<ApiResponse<UsecaseNode[]>>({
        method: 'GET',
        url: '/api/v1/usecases/tree'
      })
    },

    // 获取用例列表
    list: async (params?: { page?: number; page_size?: number; search?: string; type?: string }): Promise<ApiResponse<UsecaseNode[]>> => {
      return request<ApiResponse<UsecaseNode[]>>({
        method: 'GET',
        url: '/api/v1/usecases',
        params
      })
    },

    // 获取用例详细内容
    get: async (usecaseId: string): Promise<ApiResponse<UsecaseContent>> => {
      return request<ApiResponse<UsecaseContent>>({
        method: 'GET',
        url: `/api/v1/usecases/${usecaseId}`
      })
    },

    // 创建用例
    create: async (usecaseData: Partial<UsecaseContent>): Promise<ApiResponse<UsecaseContent>> => {
      return request<ApiResponse<UsecaseContent>>({
        method: 'POST',
        url: '/api/v1/usecases',
        data: usecaseData
      })
    },

    // 更新用例
    update: async (usecaseId: string, usecaseData: Partial<UsecaseContent>): Promise<ApiResponse<UsecaseContent>> => {
      return request<ApiResponse<UsecaseContent>>({
        method: 'PUT',
        url: `/api/v1/usecases/${usecaseId}`,
        data: usecaseData
      })
    },

    // 删除用例
    delete: async (usecaseId: string): Promise<BaseResponse> => {
      return request<BaseResponse>({
        method: 'DELETE',
        url: `/api/v1/usecases/${usecaseId}`
      })
    },

    // 复制用例
    copy: async (usecaseId: string, newName?: string): Promise<ApiResponse<UsecaseContent>> => {
      return request<ApiResponse<UsecaseContent>>({
        method: 'POST',
        url: `/api/v1/usecases/${usecaseId}/copy`,
        data: { name: newName }
      })
    },

    // 移动用例
    move: async (usecaseId: string, targetParentId: string, position?: number): Promise<BaseResponse> => {
      return request<BaseResponse>({
        method: 'PUT',
        url: `/api/v1/usecases/${usecaseId}/move`,
        data: { parent_id: targetParentId, position }
      })
    },

    // 获取用例内容（兼容旧接口）
    content: async (usecaseId: string): Promise<ApiResponse<UsecaseContent>> => {
      return request<ApiResponse<UsecaseContent>>({
        method: 'GET',
        url: `/api/v1/usecases/${usecaseId}/content`
      })
    },

    // 保存用例内容
    saveContent: async (usecaseId: string, content: Partial<UsecaseContent>): Promise<BaseResponse> => {
      return request<BaseResponse>({
        method: 'PUT',
        url: `/api/v1/usecases/${usecaseId}/content`,
        data: content
      })
    }
  },

  // 报告管理
  reports: {
    list: async (): Promise<ApiResponse<ReportData[]>> => {
      return request<ApiResponse<ReportData[]>>({
        method: 'GET',
        url: '/api/v1/reports'
      })
    },

    // 获取系统信息（用于报告生成）
    getSystems: async (): Promise<ReportSystemsResponse> => {
      return request<ReportSystemsResponse>({
        method: 'GET',
        url: '/api/v1/case-management/systems'
      })
    },

    // 生成测试用例报告
    generateTestCaseReport: async (data: GenerateReportRequest): Promise<GenerateReportResponse> => {
      return request<GenerateReportResponse>({
        method: 'POST',
        url: '/api/v1/export/test-cases',
        data
      })
    },

    // 获取报告列表
    getReportList: async (params?: {
      export_type?: string
      page?: number
      page_size?: number
      order_direction?: string
    }): Promise<ReportListResponse> => {
      return request<ReportListResponse>({
        method: 'GET',
        url: '/api/v1/export/test-cases/list',
        params
      })
    },

    // 下载报告
    downloadReport: async (exportId: string): Promise<Blob> => {
      const response = await apiClient({
        method: 'GET',
        url: '/api/v1/export/test-cases/download',
        params: { export_id: exportId },
        responseType: 'blob',
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`
        }
      })
      return response.data
    }
  },

  // 统计数据
  statistics: {
    get: async (): Promise<ApiResponse<StatisticsData>> => {
      return request<ApiResponse<StatisticsData>>({
        method: 'GET',
        url: '/api/v1/statistics'
      })
    }
  },

  // 浏览器管理
  browser: {
    status: async (): Promise<BrowserStatusResponse> => {
      return request<BrowserStatusResponse>({
        method: 'GET',
        url: '/api/v1/multi-user/browser/status'
      })
    },

    connect: async (params: { remote_host: string; remote_port: number }): Promise<BrowserStatusResponse> => {
      // 将参数放在URL查询参数中
      const queryParams = new URLSearchParams({
        remote_host: params.remote_host,
        remote_port: params.remote_port.toString()
      })

      try {
        const response = await axios({
          method: 'POST',
          url: `/api/v1/multi-user/browser/connect?${queryParams.toString()}`,
          data: {}, // 空的请求体
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${getAuthToken()}`
          }
        })

        console.log('🔗 浏览器连接原始响应:', response)
        console.log('🔗 响应数据:', response.data)

        // 直接返回响应数据，不经过request函数的数据提取
        return response.data as BrowserStatusResponse
      } catch (error: any) {
        console.error('❌ 浏览器连接API调用失败:', error)

        // 如果是HTTP错误响应，尝试提取错误信息
        if (error.response?.data) {
          throw new Error(error.response.data.message || '浏览器连接失败')
        }

        throw error
      }
    },

    disconnect: async (): Promise<BrowserDisconnectResponse> => {
      try {
        const response = await axios({
          method: 'POST',
          url: '/api/v1/multi-user/browser/disconnect',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${getAuthToken()}`
          }
        })

        console.log('🔗 浏览器断开连接原始响应:', response)
        console.log('🔗 响应数据:', response.data)

        // 直接返回响应数据，保持完整的API响应结构
        return response.data as BrowserDisconnectResponse
      } catch (error: any) {
        console.error('❌ 浏览器断开连接API调用失败:', error)

        // 如果是HTTP错误响应，尝试提取错误信息
        if (error.response?.data) {
          throw new Error(error.response.data.message || '浏览器断开连接失败')
        }

        throw error
      }
    }
  },

  // 录制管理（操作录制接口）
  recording: {
    // 开始录制 - POST /api/v1/operation-recording/start
    start: async (): Promise<StartRecordingResponse> => {
      try {
        const response = await axios({
          method: 'POST',
          url: '/api/v1/operation-recording/start',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${getAuthToken()}`
          }
        })

        console.log('🎬 开始录制原始响应:', response)
        console.log('🎬 响应数据:', response.data)

        return response.data as StartRecordingResponse
      } catch (error: any) {
        console.error('❌ 开始录制API调用失败:', error)
        if (error.response?.data) {
          throw new Error(error.response.data.message || '开始录制失败')
        }
        throw error
      }
    },

    // 暂停录制 - POST /api/v1/operation-recording/pause
    pause: async (): Promise<PauseRecordingResponse> => {
      try {
        const response = await axios({
          method: 'POST',
          url: '/api/v1/operation-recording/pause',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${getAuthToken()}`
          }
        })

        console.log('⏸️ 暂停录制原始响应:', response)
        console.log('⏸️ 响应数据:', response.data)

        return response.data as PauseRecordingResponse
      } catch (error: any) {
        console.error('❌ 暂停录制API调用失败:', error)
        if (error.response?.data) {
          throw new Error(error.response.data.message || '暂停录制失败')
        }
        throw error
      }
    },

    // 继续录制 - POST /api/v1/operation-recording/resume
    resume: async (): Promise<ResumeRecordingResponse> => {
      try {
        const response = await axios({
          method: 'POST',
          url: '/api/v1/operation-recording/resume',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${getAuthToken()}`
          }
        })

        console.log('▶️ 继续录制原始响应:', response)
        console.log('▶️ 响应数据:', response.data)

        return response.data as ResumeRecordingResponse
      } catch (error: any) {
        console.error('❌ 继续录制API调用失败:', error)
        if (error.response?.data) {
          throw new Error(error.response.data.message || '继续录制失败')
        }
        throw error
      }
    },

    // 停止录制 - POST /api/v1/operation-recording/stop
    stop: async (caseName?: string): Promise<StopRecordingResponse> => {
      try {
        const response = await axios({
          method: 'POST',
          url: '/api/v1/operation-recording/stop',
          data: caseName ? { case_name: caseName } : undefined,
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${getAuthToken()}`
          }
        })

        console.log('⏹️ 停止录制原始响应:', response)
        console.log('⏹️ 响应数据:', response.data)

        return response.data as StopRecordingResponse
      } catch (error: any) {
        console.error('❌ 停止录制API调用失败:', error)
        if (error.response?.data) {
          throw new Error(error.response.data.message || '停止录制失败')
        }
        throw error
      }
    },

    // 取消录制 - POST /api/v1/operation-recording/cancel
    cancel: async (): Promise<CancelRecordingResponse> => {
      try {
        const response = await axios({
          method: 'POST',
          url: '/api/v1/operation-recording/cancel',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${getAuthToken()}`
          }
        })

        console.log('❌ 取消录制原始响应:', response)
        console.log('❌ 响应数据:', response.data)

        return response.data as CancelRecordingResponse
      } catch (error: any) {
        console.error('❌ 取消录制API调用失败:', error)
        if (error.response?.data) {
          throw new Error(error.response.data.message || '取消录制失败')
        }
        throw error
      }
    },

    // 截图 - POST /api/v1/operation-recording/screenshot
    screenshot: async (): Promise<RecordingBaseResponse> => {
      try {
        const response = await axios({
          method: 'POST',
          url: '/api/v1/operation-recording/screenshot',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${getAuthToken()}`
          }
        })

        console.log('📸 截图原始响应:', response)
        console.log('📸 响应数据:', response.data)

        return response.data as RecordingBaseResponse
      } catch (error: any) {
        console.error('❌ 截图API调用失败:', error)
        if (error.response?.data) {
          throw new Error(error.response.data.message || '截图失败')
        }
        throw error
      }
    }
  },

  // 文件管理
  files: {
    // 文件上传
    upload: async (file: File, category?: string): Promise<FileUploadResponse> => {
      const formData = new FormData()
      formData.append('file', file)
      if (category) {
        formData.append('category', category)
      }

      return request<FileUploadResponse>({
        method: 'POST',
        url: '/api/v1/files/upload',
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
    },

    // 获取文件列表
    list: async (params?: { category?: string; page?: number; page_size?: number }): Promise<ApiResponse<FileUploadData[]>> => {
      return request<ApiResponse<FileUploadData[]>>({
        method: 'GET',
        url: '/api/v1/files',
        params
      })
    },

    // 删除文件
    delete: async (fileId: string): Promise<BaseResponse> => {
      return request<BaseResponse>({
        method: 'DELETE',
        url: `/api/v1/files/${fileId}`
      })
    },

    // 获取文件下载链接
    getDownloadUrl: async (fileId: string): Promise<ApiResponse<{ url: string }>> => {
      return request<ApiResponse<{ url: string }>>({
        method: 'GET',
        url: `/api/v1/files/${fileId}/download`
      })
    }
  },

  // 用例管理
  caseManagement: {
    /**
     * 获取系统目录结构
     * 返回系统配置和配置项信息
     */
    getSystems: async (): Promise<SystemsResponse> => {
      return request<SystemsResponse>({
        method: 'GET',
        url: '/api/v1/case-management/systems'
      })
    },

    /**
     * 创建系统
     */
    createSystem: async (systemData: CreateSystemRequest): Promise<BaseResponse> => {
      return request<BaseResponse>({
        method: 'POST',
        url: '/api/v1/case-management/systems',
        data: systemData
      })
    },

    /**
     * 更新系统
     */
    updateSystem: async (systemName: string, systemData: UpdateSystemRequest): Promise<BaseResponse> => {
      return request<BaseResponse>({
        method: 'PUT',
        url: `/api/v1/case-management/systems/${encodeURIComponent(systemName)}`,
        data: systemData
      })
    },

    /**
     * 创建配置项
     */
    createConfigItem: async (configData: CreateConfigItemRequest): Promise<BaseResponse> => {
      return request<BaseResponse>({
        method: 'POST',
        url: '/api/v1/case-management/config-items',
        data: configData
      })
    },

    /**
     * 创建测试项
     */
    createTestItem: async (testItemData: CreateTestItemRequest): Promise<BaseResponse> => {
      return request<BaseResponse>({
        method: 'POST',
        url: '/api/v1/case-management/test-items',
        data: testItemData
      })
    },

    /**
     * 设置默认用例路径
     */
    setDefaultCasePath: async (pathData: SetDefaultCasePathRequest): Promise<BaseResponse> => {
      return request<BaseResponse>({
        method: 'PUT',
        url: '/api/v1/multi-user/users/default-case-path',
        data: pathData
      })
    },

    /**
     * 更新配置项
     */
    updateConfigItem: async (configData: UpdateConfigItemRequest): Promise<BaseResponse> => {
      return request<BaseResponse>({
        method: 'PUT',
        url: '/api/v1/case-management/config-items',
        data: configData
      })
    },

    /**
     * 删除配置项
     */
    deleteConfigItem: async (configData: ConfigItemRequest): Promise<BaseResponse> => {
      return request<BaseResponse>({
        method: 'DELETE',
        url: '/api/v1/case-management/config-items',
        data: configData
      })
    },

    /**
     * 获取测试用例详情
     */
    getCaseDetail: async (tableId: string, indexId: string): Promise<CaseDetailResponse> => {
      return request<CaseDetailResponse>({
        method: 'GET',
        url: `/api/v1/case-management/test-cases/${tableId}/info/${indexId}`
      })
    },

    /**
     * 保存测试用例
     */
    saveCase: async (caseData: SaveCaseRequest): Promise<BaseResponse> => {
      return request<BaseResponse>({
        method: 'POST',
        url: '/api/v1/case-management/cases',
        data: caseData
      })
    },

    /**
     * 移动用例到不同库
     */
    moveCaseBetweenLibraries: async (data: MoveCaseBetweenLibrariesRequest): Promise<BaseResponse> => {
      return request<BaseResponse>({
        method: 'POST',
        url: '/api/v1/case-management/move-case',
        data: data
      })
    },

    /**
     * 更新用例状态
     */
    updateCaseStatus: async (data: UpdateCaseStatusRequest): Promise<BaseResponse> => {
      return request<BaseResponse>({
        method: 'POST',
        url: '/api/v1/case-management/update-case-status',
        data: data
      })
    },

    /**
     * 批量更新用例状态
     */
    batchUpdateCaseStatus: async (data: UpdateCaseStatusRequest[]): Promise<BaseResponse> => {
      return request<BaseResponse>({
        method: 'POST',
        url: '/api/v1/case-management/batch-update-case-status',
        data: { updates: data }
      })
    },

    /**
     * 保存用例详情
     */
    saveCaseDetail: async (tableId: string, caseData: any): Promise<BaseResponse> => {
      return request<BaseResponse>({
        method: 'POST',
        url: '/api/v1/case-management/cases',
        data: {
          tableId,
          caseData
        }
      })
    },

    /**
     * 创建用例
     */
    createCase: async (caseData: CreateCaseRequest): Promise<BaseResponse> => {
      return request<BaseResponse>({
        method: 'POST',
        url: '/api/v1/case-management/cases/create',
        data: caseData
      })
    },

    /**
     * 删除用例
     */
    deleteCase: async (caseData: DeleteCaseRequest): Promise<BaseResponse> => {
      return request<BaseResponse>({
        method: 'DELETE',
        url: '/api/v1/case-management/cases',
        data: caseData
      })
    },

    /**
     * 恢复用例
     */
    restoreCase: async (caseData: RestoreCaseRequest): Promise<BaseResponse> => {
      return request<BaseResponse>({
        method: 'POST',
        url: '/api/v1/case-management/cases/restore',
        data: caseData
      })
    },

    /**
     * 获取指定版本的用例详情
     */
    getCaseDetailByVersion: async (tableId: string, caseInfoId: string): Promise<CaseDetailResponse> => {
      return request<CaseDetailResponse>({
        method: 'GET',
        url: `/api/v1/case-management/test-cases/${tableId}/case/${caseInfoId}`
      })
    },

    /**
     * 选择/取消选择用例
     */
    selectCase: async (selectionData: CaseSelectionRequest): Promise<BaseResponse> => {
      return request<BaseResponse>({
        method: 'PUT',
        url: '/api/v1/case-management/cases/selection',
        data: selectionData
      })
    },

    /**
     * 调整用例顺序
     */
    updateCaseOrder: async (orderData: CaseOrderRequest): Promise<BaseResponse> => {
      return request<BaseResponse>({
        method: 'PUT',
        url: '/api/v1/case-management/cases/order',
        data: orderData
      })
    }
  },

  // 用户管理别名（为了兼容UserManagement.vue）
  get userManagement() {
    return this.permission
  }
}

// 导出默认的 apiClient 实例（向后兼容）
export default apiClient

// 导出其他必要的工具函数和常量
export {
  getAuthToken,
  request,
  API_BASE_URL,
  API_TIMEOUT
}

// 注意：类型定义已经通过 export interface 导出，无需重复导出
